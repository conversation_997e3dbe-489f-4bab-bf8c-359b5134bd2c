import 'package:class_z/core/imports.dart';
import 'package:class_z/core/common/data/models/classDate_model.dart';

class TimeSloCentre extends StatefulWidget {
  final String classId;
  const TimeSloCentre({required this.classId, super.key});

  @override
  State<TimeSloCentre> createState() => _TimeSloCentreState();
}

class _TimeSloCentreState extends State<TimeSloCentre> {
  DateTime _focusedDay = DateTime.now();
  DateTime? _selectedDay;
  late ValueNotifier<List<EventModel>> _selectedEvents;
  List<EventModel> events = [];
  EventDatesModel? eventDates;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _selectedDay = _focusedDay;
    _selectedEvents = ValueNotifier<List<EventModel>>([]);
    _fetchEvents();
  }

  @override
  void dispose() {
    _selectedEvents.dispose();
    super.dispose();
  }

  void _fetchEvents() {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    context
        .read<CenterBloc>()
        .add(GetEventsByClassIdEvent(classId: widget.classId));
  }

  // Callback when a day is selected
  void _onselectedDay(DateTime selectedDay, DateTime focusedDay) {
    setState(() {
      _selectedDay = selectedDay;
      _focusedDay = focusedDay;
      _selectedEvents.value = _getEventsForDay(selectedDay);
    });
  }

  // Function to get events for a given day
  List<EventModel> _getEventsForDay(DateTime dateTime) {
    return events
        .where((event) =>
            event.date?.year == dateTime.year &&
            event.date?.month == dateTime.month &&
            event.date?.day == dateTime.day)
        .toList();
  }

  // Helper method to extract dates from events for the calendar
  List<String> _getEventDates() {
    final List<String> dates = [];
    print('fetched events $events');
    if (eventDates?.eventDates != null) {
      for (var event in eventDates!.eventDates!) {
        if (event.dateId?.date != null) {
          // Try to parse and reformat if not already yyyy-MM-dd
          String raw = event.dateId!.date!;
          DateTime? dt;
          // Try parsing as yyyy-MM-dd first
          dt = DateTime.tryParse(raw);
          if (dt == null) {
            // Try parsing as yy/MM/dd
            final parts = raw.split('/');
            if (parts.length == 3) {
              int day = int.parse(parts[0]);
              int month = int.parse(parts[1]);
              int year = int.parse(parts[2]);
              if (year < 100) year += 2000; // crude century fix
              dt = DateTime(year, month, day);
            }
          }
          if (dt != null) {
            final dateStr = DateFormat('yyyy-MM-dd').format(dt);
            if (!dates.contains(dateStr)) {
              dates.add(dateStr);
            }
          }
        }
      }
    }
    print('new dates $dates');
    return dates;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocListener<CenterBloc, CenterState>(
        listener: (context, state) {
          print('current state $state');
          if (state is CenterLoadingState) {
            setState(() {
              _isLoading = true;
            });
            loadingState(context: context);
          } else {
            setState(() {
              _isLoading = false;
            });
            hideLoadingDialog(context);
          }

          if (state is CenterErrorState) {
            setState(() {
              _errorMessage = state.message;
            });
            errorState(context: context, error: state.message);
          }

          // if (state is EventListFetchSuccess) {
          //   setState(() {
          //     print('got ${state.events.length}');
          //     events = state.events;
          //     _selectedEvents.value =
          //         _getEventsForDay(_selectedDay ?? _focusedDay);
          //   });
          // }
          if (state is EventDatesForClassSuccess) {
            setState(() {
              eventDates = state.eventsDate;
              print('here is dates of event $eventDates');
            });
          }
        },
        child: RefreshIndicator(
          onRefresh: () async {
            _fetchEvents();
          },
          child: SingleChildScrollView(
            physics: AlwaysScrollableScrollPhysics(),
            child: Column(
              children: [
                CustomAppBarDouble(
                  title: "Class of Your Choice",
                  title2: "Choose your interest class",
                  leading: customBackButton(),
                ),
                SizedBox(height: 24.h),
                buildProgressSteps(
                  context: context,
                  cont1: AppPallete.secondaryColor,
                  cont2: AppPallete.dividerTime,
                  cont3: AppPallete.dividerTime,
                ),
                SizedBox(height: 18.h),
                CustomCalendar(
                  focusedDay: _focusedDay,
                  selectedDay: _selectedDay,
                  onDaySelected: _onselectedDay,
                  eventDates: _getEventDates(),
                ),
                // Show dateId info for selected date
                Builder(
                  builder: (context) {
                    if (_selectedDay == null || eventDates?.eventDates == null)
                      return SizedBox.shrink();
                    ClassDate? selectedClassDate;
                    for (var ed in eventDates!.eventDates!) {
                      if (ed.dates != null) {
                        for (var d in ed.dates!) {
                          if (d.year == _selectedDay!.year &&
                              d.month == _selectedDay!.month &&
                              d.day == _selectedDay!.day) {
                            selectedClassDate = ed.dateId;
                          }
                        }
                      }
                    }
                    if (selectedClassDate == null) return SizedBox.shrink();
                    return Column(
                      children: [
                        Padding(
                          padding: EdgeInsets.symmetric(
                              vertical: 12.h, horizontal: 24.w),
                          child: Container(
                            padding: EdgeInsets.all(16.w),
                            decoration: BoxDecoration(
                              color: Colors.grey[100],
                              borderRadius: BorderRadius.circular(10.r),
                              border: Border.all(color: Colors.grey[300]!),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text('Class Date Info:',
                                    style:
                                        TextStyle(fontWeight: FontWeight.bold)),
                                SizedBox(height: 8.h),
                                if (selectedClassDate.startTime != null)
                                  Text('Start: ${selectedClassDate.startTime}'),
                                if (selectedClassDate.endTime != null)
                                  Text('End: ${selectedClassDate.endTime}'),
                                if (selectedClassDate.durationMinutes != null)
                                  Text(
                                      'Duration: ${selectedClassDate.durationMinutes} min'),
                                if (selectedClassDate.date != null)
                                  Text('Date: ${selectedClassDate.date}'),
                              ],
                            ),
                          ),
                        ),
                        // Show centreList for the selected date
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 24.w),
                          child: centreList(
                            context: context,
                            title: 'Class',
                            time: selectedClassDate.startTime ?? '',
                            minute: selectedClassDate.durationMinutes ?? '',
                            imagePath: '', // Provide image if available
                            category: '', // Provide category if available
                            name: '', // Provide coach name if available
                            location: '', // Provide location if available
                            language: '', // Provide language if available
                            ageGroup: '', // Provide age group if available
                            currentStudent:
                                selectedClassDate.students?.length.toString() ??
                                    '0',
                            totalStudent:
                                '', // Provide total students if available
                            cost: '', // Provide cost if available
                            sen: false, // Provide SEN if available
                            onTap: () {},
                          ),
                        ),
                      ],
                    );
                  },
                ),
                SizedBox(height: 22.h),
                customDivider(width: 406.w, padding: 12.w),
                SizedBox(height: 22.h),

                // Show loading indicator, error message, or events
                if (_isLoading && events.isEmpty)
                  Center(
                    child: CircularProgressIndicator(),
                  )
                else if (_errorMessage != null && events.isEmpty)
                  Center(
                    child: Column(
                      children: [
                        Icon(Icons.error_outline, size: 48, color: Colors.red),
                        SizedBox(height: 16),
                        Text(
                          "Error loading events",
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(height: 8),
                        Text(_errorMessage ?? "Unknown error"),
                        SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: _fetchEvents,
                          child: Text("Try Again"),
                        ),
                      ],
                    ),
                  )
                else
                  // List of events for the selected day
                  ValueListenableBuilder<List<EventModel>>(
                    valueListenable: _selectedEvents,
                    builder: (context, value, _) {
                      if (value.isEmpty) {
                        return Container(
                          height: 200,
                          alignment: Alignment.center,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.event_busy,
                                size: 48,
                                color: Colors.grey,
                              ),
                              SizedBox(height: 16),
                              Text(
                                "No events available for selected date",
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.grey[700],
                                ),
                              ),
                              SizedBox(height: 8),
                              Text(
                                "Please select another date",
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        );
                      }

                      return Padding(
                        padding: EdgeInsets.only(left: 0.w),
                        child: ListView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          padding: EdgeInsets.zero,
                          itemBuilder: (context, index) {
                            final event = value[index];
                            return centreList(
                              context: context,
                              title: event.title ?? "No Title",
                              time: event.startTime ?? "",
                              minute: event.durationMinutes ?? "",
                              imagePath: event.classId?.mainImage?.url !=
                                          null &&
                                      event.classId!.mainImage!.url!.isNotEmpty
                                  ? imageStringGenerator(
                                      imagePath: event.classId!.mainImage!.url!)
                                  : ImagePath.charlie,
                              category: event.classId?.level ?? "No Category",
                              name: event.classId?.coach?.displayName ??
                                  "No Coach",
                              location: event.classId?.center?.displayName ??
                                  "No Location",
                              language:
                                  event.classId?.language?.isNotEmpty == true
                                      ? event.classId!.language!.first
                                      : "Not specified",
                              ageGroup: event.classId?.ageFrom != null &&
                                      event.classId?.ageTo != null
                                  ? "Age ${event.classId?.ageFrom}-${event.classId?.ageTo}"
                                  : "Age not specified",
                              currentStudent:
                                  event.dateId?.students?.length.toString() ??
                                      "0",
                              totalStudent:
                                  event.classId?.numberOfStudent.toString() ??
                                      "0",
                              cost: event.classId?.charge.toString() ?? "0",
                              sen: event.classId?.sen ?? false,
                              onTap: () {
                                NavigatorService.pushNamed(
                                  AppRoutes.request,
                                  arguments: {
                                    'classModel': event.classId,
                                    'eventDetails': [event],
                                  },
                                );
                              },
                            );
                          },
                          itemCount: value.length,
                        ),
                      );
                    },
                  ),

                SizedBox(height: 50.h), // Add bottom padding
              ],
            ),
          ),
        ),
      ),
    );
  }
}
