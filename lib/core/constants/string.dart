class AppText {
  // Store the raw URL value - ensure correct domain

  // static final String _rawDeviceUrl =
  //   "https://80f0-2a09-bac5-5907-323-00-50-d1.ngrok-free.app";

  //static final String _rawDeviceUrl = "https://api.classz.co";

  // For local development (uncomment when needed):
  // static final String _rawDeviceUrl = "http://140.118.216.2:3000";
  // static final String _rawDeviceUrl = "http://192.168.31.53:3000";
  // static final String _rawDeviceUrl = "http://172.16.0.171:3000";
  //static final String _rawDeviceUrl = "http://172.20.10.12:3000";
  static final String _rawDeviceUrl = "http://100.69.15.232:3000";
  // static final String _rawDeviceUrl =
  //     "https://a30f-2a09-bac1-7440-38-00-50-1aa.ngrok-free.app";

  // Create a getter that always returns the sanitized URL
  static String get device {
    // Changed here: Don't look for "classz.cod" in the URL to fix - the base URL is already correct
    return _rawDeviceUrl;
  }

  // Set the device URL with proper sanitization
  static set device(String value) {
    if (value.contains("classz.cod")) {
      print("Warning: Setting device URL with incorrect domain, fixing typo");
      // Don't set the _rawDeviceUrl if there's a typo - just log the warning
    }
  }

  // Improved URL sanitizer method
  static String sanitizeUrl(String url) {
    if (url.isEmpty) return url;

    // Fix the common API typo - this is the key fix
    if (url.contains("api.classz.cod")) {
      print("🔄 Fixing API URL typo: $url");
      return url.replaceAll("api.classz.cod", "api.classz.co");
    }

    // Fix any line breaks in URLs
    if (url.contains('\n')) {
      return url.replaceAll('\n', '');
    }

    // Fix spaces in the URL which can cause issues
    if (url.contains(' ')) {
      return url.replaceAll(' ', '');
    }

    return url;
  }

  static String token = "";
  static const String centreDes =
      "Welcome to the Enrichment Hub!Our centre is designed to provide a stimulating and supportive environment where learners of all ages can engage in a wide range of captivating classes and pursue their unique interests.";
  static const String classLanguage = "Class Language";
  static const String cantonese = "Cantonese";
  static const String english = "English";
  static const String mandarin = "Mandarin";
  static const String name = "Charlie Own";
  static const String skills = "Skills";
  static const String experience = "10 years experience";
  static const String register1 =
      "Registered member at ABC Badminton Association";
  static const String centre = "ABC testing Centre";
  static const String centreLocation = "Tsim Sa Tsui";
  static const String centreSkills = "Piano, Violin, Guitar, Flute...";
  static const String review =
      "asdasdnjaksdkansfkjlensdadsmn,nfjskednmkjfasdashbjakdasdajknahdfkjbhkjbjnhsknafbhnbhfjnsnsasdasdnjaksdkansfkjlensdadsmn";
  static const String bio =
      "sagfdhgjhjnmbvcfghbnvcfgdhtghjbnvcfghjbnvfghcfgjhvbnghjfbvghsagfdhgjhjnmbvcfghbnvcfgdhtghjbnvcfghjbnvfghcfgjhvbnghjfbvghsagfdhgjhjnmbvcfghbnvcfgdhtghjbnvcfghjbnvfghcfgjhvbnghjfbvghsagfdhgjhjnmbvcfghbnvcfgdhtghjbnvcfghjbnvfghcfgjhvbnghjfbvghsagfdhgjhjnmbvcfghbnvcfgdhtghjbnvcfghjbnvfghcfgjhvbnghjfbvghsdfdsfsdfsdfkjhjhknbvcgftyhjgfty";

  static const String requestModelocation =
      "XXX Building, ABC Street, Tseung Kwan O";
  static const String requestModeinfo =
      "Accurate location will be received after confirmation";
  static const String participate = "Participate in external competition";
  static const String internal = "Provide internal competition/ event";
  static const String smallClass = "Small-class teaching";
  static const String senPrac = "SEN teaching";
  static const String private = "Private class available";
  static const String equipment = "Equipment provided";
  static const String daycamp = "Day camp";
  static const String babySitting = "Babysitting service";
  static const String childFriendly = "Use of child-friendly supplies";
  static const String moreInformationlocation =
      "G032, G/F, ABC Mall, Causeway Bay, Hong Kong";
  static const String moreInfoNumber = "+852 1234 5678";
  static const String moreInfoDate = "Mon-Sun 08:00-19:00";
  static const String a = "";
  static const String terms = """
TERMS AND CONDITIONS
These Terms and Conditions ("Agreement") govern your use of the ClassZ interest class reservation platform ("Platform") provided by [Your Company Name] ("Company"). By accessing or using the Platform, you agree to be bound by this Agreement. If you do not agree with these terms, please refrain from using the Platform.

1. Definitions
a. "Platform" refers to the ClassZ interest class reservation platform provided by the Company.
b. "User" refers to any individual who uses the Platform to search, book, or cancel interest classes.
c. "Provider" refers to both freelance coaches and registered centres who offer interest classes through the Platform.
d. "Class" refers to the interest classes offered by the Providers.
e. "Location" refers to the physical venue where the Class takes place.

2. Platform Services
a. The Platform provides Users with a platform to search, book, and cancel interest classes at locations of their choice.
b. Users may choose between freelance coaches or registered centres as their preferred Providers.

3. User Responsibility
a. Users are responsible for their choice of Class and Location.
b. Users must exercise due diligence in selecting a safe and appropriate Location for the Class.
c. Users must ensure the safety and suitability of the Location for themselves or their children.
d. Users acknowledge that the Company does not guarantee the safety or suitability of any Location.

4. Provider Responsibility
a. Providers are responsible for ensuring the safety and suitability of the Location for conducting Classes.
b. Providers must comply with all applicable laws, regulations, and guidelines in operating their Classes.
c. Providers must obtain any necessary permits, licenses, or approvals required to conduct Classes at a specific Location.

5. Company's Role
a. The Company acts solely as an intermediary, connecting Users with Providers through the Platform.
b. The Company does not own, operate, or control the Locations where the Classes are conducted.
c. The Company does not endorse, guarantee, or warrant the safety or suitability of any Location.

6. Limitation of Liability
a. To the maximum extent permitted by law, the Company shall not be liable for any direct, indirect, incidental, consequential, or special damages arising out of or in connection with the use of the Platform or any Location chosen by the User or Provider.
b. Users and Providers agree to indemnify and hold the Company harmless from any claims, damages, or liabilities arising out of or related to their choice of Location.
c. The Company acts as a mediator between Users and Providers on the Platform and is not responsible for any issues related to the content, teaching methods, or frameworks of the Classes offered by Providers.
d. The Company does not endorse, guarantee, or warrant the accuracy, quality, or effectiveness of the Classes or the performance of the Providers.
e. Providers are solely responsible for the content they teach, the design of their frameworks, and any charges or misconduct issues that may arise.
f. The Company shall not be liable for any direct, indirect, incidental, consequential, or special damages arising out of or in connection with the Classes or the conduct of the Providers.
g. Users acknowledge that their interactions and engagements with Providers are at their own risk and discretion.

7. Dispute Resolution
a. Any dispute arising out of or in connection with this Agreement shall be resolved through negotiation in good faith.
b. In the event that a dispute cannot be resolved through negotiation, the parties agree to submit to the exclusive jurisdiction of the courts in Hong Kong.

8. Modifications to the Agreement
a. The Company reserves the right to modify or amend this Agreement at any time.
b. Updated versions of the Agreement will be posted on the Platform, and it is the User's responsibility to review the Agreement periodically.
c. Continued use of the Platform after any modifications to the Agreement shall constitute acceptance of the modified terms.

9. Governing Law
a. This Agreement shall be governed by and construed in accordance with the laws of Hong Kong.

By using the Platform, you acknowledge that you have read, understood, and agreed to be bound by this Agreement. If you do not agree with any part of this Agreement, please refrain from using the Platform.

ClassZ
[Address]
[City, State, ZIP]
[Email Address]
[Phone Number]
[Website]
""";
  List<String> services = [
    "Centre assists students to participate in external competitions",
    "Centre provides internal competitions/events regularly",
    "Equipment is provided for students within classes",
    "Day camp training is held regularly for students",
    "Centre provides use of child-friendly supplies (e.g. desk, chair)",
    "Centre provides babysitting service (allows students to stay behind after class)",
  ];
}
