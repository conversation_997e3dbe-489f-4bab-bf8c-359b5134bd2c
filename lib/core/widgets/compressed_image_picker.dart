import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:class_z/core/services/image_compression_service.dart';

enum ImagePickerType {
  profile,
  mainImage,
  gallery,
  custom,
}

class CompressedImagePicker {
  static final ImagePicker _picker = ImagePicker();

  /// Pick and compress a single image
  static Future<CompressedImageResult> pickAndCompressImage({
    required ImageSource source,
    ImagePickerType type = ImagePickerType.mainImage,
    int? customQuality,
    int? customMaxWidth,
    int? customMaxHeight,
    int? customTargetSize,
    Function(String)? onProgress,
  }) async {
    try {
      onProgress?.call('Selecting image...');

      // Pick image with timeout
      final XFile? pickedFile = await _picker
          .pickImage(
        source: source,
        imageQuality: 90, // Initial quality to reduce file size during picking
      )
          .timeout(
        const Duration(seconds: 30),
        onTimeout: () {
          throw Exception('Image selection timed out');
        },
      );

      if (pickedFile == null) {
        return CompressedImageResult.cancelled();
      }

      onProgress?.call('Processing image...');

      final originalFile = File(pickedFile.path);
      final originalSize = await originalFile.length();

      // Check file size limit (10MB max)
      if (originalSize > 10 * 1024 * 1024) {
        return CompressedImageResult.error('Image too large (max 10MB)');
      }

      // Check if compression is needed
      final needsCompression = await ImageCompressionService.needsCompression(
        originalFile,
        maxSize: customTargetSize,
      );

      if (!needsCompression && type != ImagePickerType.custom) {
        onProgress?.call('Image already optimized!');
        return CompressedImageResult.success(
          originalFile,
          originalSize,
          originalSize,
          compressionRatio: 0.0,
        );
      }

      onProgress?.call('Compressing image...');

      File? compressedFile;

      // Apply compression with timeout
      try {
        switch (type) {
          case ImagePickerType.profile:
            compressedFile =
                await ImageCompressionService.compressProfileImage(originalFile)
                    .timeout(const Duration(seconds: 15));
            break;
          case ImagePickerType.mainImage:
            compressedFile =
                await ImageCompressionService.compressMainImage(originalFile)
                    .timeout(const Duration(seconds: 15));
            break;
          case ImagePickerType.gallery:
            compressedFile =
                await ImageCompressionService.compressGalleryImage(originalFile)
                    .timeout(const Duration(seconds: 20));
            break;
          case ImagePickerType.custom:
            compressedFile = await ImageCompressionService.compressImage(
              originalFile,
              quality: customQuality,
              maxWidth: customMaxWidth,
              maxHeight: customMaxHeight,
              targetSizeBytes: customTargetSize,
            ).timeout(const Duration(seconds: 20));
            break;
        }
      } catch (e) {
        if (e.toString().contains('timeout')) {
          // If compression times out, return original file
          onProgress?.call('Using original image (compression timed out)');
          return CompressedImageResult.success(
            originalFile,
            originalSize,
            originalSize,
            compressionRatio: 0.0,
          );
        }
        rethrow;
      }

      if (compressedFile == null) {
        // If compression fails, return original file
        onProgress?.call('Using original image (compression failed)');
        return CompressedImageResult.success(
          originalFile,
          originalSize,
          originalSize,
          compressionRatio: 0.0,
        );
      }

      final compressedSize = await compressedFile.length();
      final compressionRatio =
          ((originalSize - compressedSize) / originalSize * 100);

      onProgress?.call('Compression complete!');

      return CompressedImageResult.success(
        compressedFile,
        originalSize,
        compressedSize,
        compressionRatio: compressionRatio,
      );
    } catch (e) {
      print('📸 Error in pickAndCompressImage: $e');
      return CompressedImageResult.error('Error processing image: $e');
    }
  }

  /// Pick and compress multiple images
  static Future<List<CompressedImageResult>> pickAndCompressMultipleImages({
    ImagePickerType type = ImagePickerType.gallery,
    int? customQuality,
    int? customMaxWidth,
    int? customMaxHeight,
    int? customTargetSize,
    Function(String)? onProgress,
  }) async {
    try {
      onProgress?.call('Selecting images...');

      final List<XFile> pickedFiles = await _picker.pickMultiImage(
        imageQuality: 90,
      );

      if (pickedFiles.isEmpty) {
        return [CompressedImageResult.cancelled()];
      }

      final results = <CompressedImageResult>[];

      for (int i = 0; i < pickedFiles.length; i++) {
        onProgress?.call('Processing image ${i + 1}/${pickedFiles.length}...');

        final originalFile = File(pickedFiles[i].path);
        final originalSize = await originalFile.length();

        File? compressedFile;

        // Apply compression based on type
        switch (type) {
          case ImagePickerType.profile:
            compressedFile = await ImageCompressionService.compressProfileImage(
                originalFile);
            break;
          case ImagePickerType.mainImage:
            compressedFile =
                await ImageCompressionService.compressMainImage(originalFile);
            break;
          case ImagePickerType.gallery:
            compressedFile = await ImageCompressionService.compressGalleryImage(
                originalFile);
            break;
          case ImagePickerType.custom:
            compressedFile = await ImageCompressionService.compressImage(
              originalFile,
              quality: customQuality,
              maxWidth: customMaxWidth,
              maxHeight: customMaxHeight,
              targetSizeBytes: customTargetSize,
            );
            break;
        }

        if (compressedFile != null) {
          final compressedSize = await compressedFile.length();
          final compressionRatio =
              ((originalSize - compressedSize) / originalSize * 100);

          results.add(CompressedImageResult.success(
            compressedFile,
            originalSize,
            compressedSize,
            compressionRatio: compressionRatio,
          ));
        } else {
          results.add(
              CompressedImageResult.error('Failed to compress image ${i + 1}'));
        }
      }

      onProgress?.call('All images processed!');
      return results;
    } catch (e) {
      return [CompressedImageResult.error('Error processing images: $e')];
    }
  }

  /// Show image picker dialog with compression options
  static Future<CompressedImageResult?> showImagePickerDialog({
    required BuildContext context,
    ImagePickerType type = ImagePickerType.mainImage,
    int? customQuality,
    int? customMaxWidth,
    int? customMaxHeight,
    int? customTargetSize,
    bool showCompressionInfo = true,
  }) async {
    return showDialog<CompressedImageResult>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Select Image'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (showCompressionInfo) ...[
                const Text(
                  'Images will be automatically compressed for optimal performance.',
                  style: TextStyle(fontSize: 12, color: Colors.grey),
                ),
                const SizedBox(height: 16),
              ],
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildPickerButton(
                    context,
                    'Camera',
                    Icons.camera_alt,
                    ImageSource.camera,
                    type,
                    customQuality,
                    customMaxWidth,
                    customMaxHeight,
                    customTargetSize,
                  ),
                  _buildPickerButton(
                    context,
                    'Gallery',
                    Icons.photo_library,
                    ImageSource.gallery,
                    type,
                    customQuality,
                    customMaxWidth,
                    customMaxHeight,
                    customTargetSize,
                  ),
                ],
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                if (context.mounted) {
                  Navigator.of(context).pop(CompressedImageResult.cancelled());
                }
              },
              child: const Text('Cancel'),
            ),
          ],
        );
      },
    );
  }

  static Widget _buildPickerButton(
    BuildContext context,
    String label,
    IconData icon,
    ImageSource source,
    ImagePickerType type,
    int? customQuality,
    int? customMaxWidth,
    int? customMaxHeight,
    int? customTargetSize,
  ) {
    return Column(
      children: [
        IconButton(
          onPressed: () async {
            // Check if context is still mounted before using Navigator
            if (context.mounted) {
              Navigator.of(context).pop(); // Close dialog first
            }

            // Show progress dialog
            showDialog(
              context: context,
              barrierDismissible: false,
              builder: (context) => const AlertDialog(
                content: Row(
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(width: 16),
                    Text('Processing image...'),
                  ],
                ),
              ),
            );

            final result = await pickAndCompressImage(
              source: source,
              type: type,
              customQuality: customQuality,
              customMaxWidth: customMaxWidth,
              customMaxHeight: customMaxHeight,
              customTargetSize: customTargetSize,
            );

            // Check if context is still mounted before using Navigator
            if (context.mounted) {
              Navigator.of(context).pop(); // Close progress dialog
              Navigator.of(context).pop(result); // Return result
            }
          },
          icon: Icon(icon, size: 40),
        ),
        Text(label),
      ],
    );
  }
}

class CompressedImageResult {
  final File? file;
  final int originalSize;
  final int compressedSize;
  final double compressionRatio;
  final String? error;
  final bool isSuccess;
  final bool isCancelled;

  CompressedImageResult._({
    this.file,
    this.originalSize = 0,
    this.compressedSize = 0,
    this.compressionRatio = 0.0,
    this.error,
    this.isSuccess = false,
    this.isCancelled = false,
  });

  factory CompressedImageResult.success(
    File file,
    int originalSize,
    int compressedSize, {
    required double compressionRatio,
  }) {
    return CompressedImageResult._(
      file: file,
      originalSize: originalSize,
      compressedSize: compressedSize,
      compressionRatio: compressionRatio,
      isSuccess: true,
    );
  }

  factory CompressedImageResult.error(String error) {
    return CompressedImageResult._(
      error: error,
    );
  }

  factory CompressedImageResult.cancelled() {
    return CompressedImageResult._(
      isCancelled: true,
    );
  }

  String get compressionInfo {
    if (!isSuccess || file == null) return '';
    return 'Original: ${(originalSize / 1024).toStringAsFixed(1)}KB → '
        'Compressed: ${(compressedSize / 1024).toStringAsFixed(1)}KB '
        '(${compressionRatio.toStringAsFixed(1)}% reduction)';
  }
}
