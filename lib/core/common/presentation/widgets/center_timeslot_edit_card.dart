import 'package:class_z/core/imports.dart';

// IMPROVED: Modern card-based design for coach timeslot with better UX/UI
// Removed complex Stack positioning and text overlays for better readability
// Uses clean card layout with proper spacing and hierarchy
Widget centerTimeSlotEditCard({
  required BuildContext context,
  required String imagepath,
  required bool edit,
  required bool private,
  required String course,
  required String level,
  required String name,
  required String duration,
  required String classTime,
  required String location,
  required String language,
  required int totalStudent,
  required String ageGroup,
  required int student,
  required int fee,
  required VoidCallback onTap,
}) {
  // Determine status
  final bool isFull = student >= totalStudent && totalStudent > 0;
  final bool isPrivate = private;

  return Container(
    margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
    child: Material(
      color: Colors.transparent,
      child: InkWell(
        borderRadius: BorderRadius.circular(16.r),
        onTap: onTap,
        child: Container(
          decoration: BoxDecoration(
            color: isPrivate ? AppPallete.color255 : Colors.white,
            borderRadius: BorderRadius.circular(16.r),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.08),
                blurRadius: 8,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Time and Edit Column
              Container(
                width: 80.w,
                padding: EdgeInsets.all(16.w),
                child: Column(
                  children: [
                    Text(
                      classTime,
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w600,
                        color: AppPallete.secondaryColor,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    if (edit) ...[
                      SizedBox(height: 12.h),
                      Container(
                        width: 24.w,
                        height: 24.h,
                        decoration: BoxDecoration(
                          color: AppPallete.red,
                          borderRadius: BorderRadius.circular(12.r),
                        ),
                        child: Icon(
                          Icons.remove,
                          size: 14.w,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ],
                ),
              ),

              // Main Content
              Expanded(
                child: Padding(
                  padding: EdgeInsets.all(16.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header Section
                      Row(
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  course,
                                  style: TextStyle(
                                    fontSize: 16.sp,
                                    fontWeight: FontWeight.w700,
                                    color: Colors.black87,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                SizedBox(height: 2.h),
                                Text(
                                  level,
                                  style: TextStyle(
                                    fontSize: 13.sp,
                                    fontWeight: FontWeight.w400,
                                    color: Colors.black54,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(width: 8.w),
                          // Location
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              customSvgPicture(
                                imagePath: ImagePath.locationSvg,
                                height: 12.h,
                                width: 8.w,
                                color: AppPallete.change,
                              ),
                              SizedBox(width: 4.w),
                              Text(
                                location,
                                style: TextStyle(
                                  fontSize: 12.sp,
                                  fontWeight: FontWeight.w400,
                                  color: AppPallete.change,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),

                      SizedBox(height: 16.h),

                      // Image Section with overlays (like center cards)
                      ClipRRect(
                        borderRadius: BorderRadius.circular(12.r),
                        child: Stack(
                          children: [
                            CustomImageBuilder(
                              imagePath: imagepath,
                              borderRadius: 0,
                              height: 100.h,
                              width: double.infinity,
                            ),
                            // Status badge (top-left)
                            Positioned(
                              top: 8.h,
                              left: 8.w,
                              child: Container(
                                padding: EdgeInsets.symmetric(
                                    horizontal: 8.w, vertical: 4.h),
                                decoration: BoxDecoration(
                                  color: isFull
                                      ? AppPallete.red
                                      : AppPallete.secondaryColor,
                                  borderRadius: BorderRadius.circular(12.r),
                                ),
                                child: Text(
                                  isFull ? 'FULL' : 'AVAILABLE',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 10.sp,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                            ),
                            // Duration badge (top-right)
                            Positioned(
                              top: 8.h,
                              right: 8.w,
                              child: Container(
                                padding: EdgeInsets.symmetric(
                                    horizontal: 8.w, vertical: 4.h),
                                decoration: BoxDecoration(
                                  color: Colors.black.withValues(alpha: 0.7),
                                  borderRadius: BorderRadius.circular(12.r),
                                ),
                                child: Text(
                                  duration,
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 10.sp,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ),
                            // SEN badge (below duration, top-right)
                            Positioned(
                              top: 36.h,
                              right: 8.w,
                              child: customSen(),
                            ),
                          ],
                        ),
                      ),

                      SizedBox(height: 16.h),

                      // Coach and Details Section
                      Text(
                        'by $name',
                        style: TextStyle(
                          fontSize: 13.sp,
                          fontWeight: FontWeight.w500,
                          color: Colors.black87,
                        ),
                      ),

                      SizedBox(height: 12.h),

                      // Bottom Info Row
                      Row(
                        children: [
                          // Student count
                          Row(
                            children: [
                              customSvgPicture(
                                imagePath: ImagePath.peopleSvg,
                                height: 14.h,
                                width: 14.w,
                                color: AppPallete.secondaryColor,
                              ),
                              SizedBox(width: 4.w),
                              Text(
                                isFull
                                    ? "$totalStudent (Full)"
                                    : "$student/$totalStudent",
                                style: TextStyle(
                                  fontSize: 13.sp,
                                  fontWeight: FontWeight.w600,
                                  color:
                                      isFull ? AppPallete.red : Colors.black87,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(width: 16.w),
                          // Age group
                          Text(
                            'Age $ageGroup',
                            style: TextStyle(
                              fontSize: 13.sp,
                              fontWeight: FontWeight.w500,
                              color: Colors.black54,
                            ),
                          ),
                          Spacer(),
                          // Price and language
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  customSvgPicture(
                                    imagePath: ImagePath.zSvg,
                                    height: 14.h,
                                    width: 14.w,
                                    color: AppPallete.secondaryColor,
                                  ),
                                  SizedBox(width: 4.w),
                                  Text(
                                    '$fee',
                                    style: TextStyle(
                                      fontSize: 14.sp,
                                      fontWeight: FontWeight.w700,
                                      color: AppPallete.secondaryColor,
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 2.h),
                              Text(
                                language,
                                style: TextStyle(
                                  fontSize: 11.sp,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.black54,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    ),
  );
}
