import 'package:class_z/core/imports.dart';

// IMPROVED: Modern card-based design with better UX/UI
// This widget now uses a clean card layout with proper spacing and hierarchy
// Removed complex Stack positioning and text overlays for better readability
Widget centreList(
    {required BuildContext context,
    required String title,
    required String time,
    required String minute,
    required String imagePath,
    required String category,
    required String name,
    required String location,
    required String language,
    required String ageGroup,
    required String currentStudent,
    required String totalStudent,
    required String cost,
    required bool sen,
    required VoidCallback onTap}) {
  // Determine availability status
  final bool isFull = currentStudent == totalStudent;

  return Container(
    margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
    child: Material(
      color: Colors.transparent,
      child: InkWell(
        borderRadius: BorderRadius.circular(16.r),
        onTap: onTap,
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16.r),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.08),
                blurRadius: 8,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Image and status section
              Stack(
                children: [
                  ClipRRect(
                    borderRadius:
                        BorderRadius.vertical(top: Radius.circular(16.r)),
                    child: CustomImageBuilder(
                      imagePath: imagePath,
                      height: 120.h,
                      width: double.infinity,
                      borderRadius: 0,
                    ),
                  ),
                  // Status badges
                  Positioned(
                    top: 12.h,
                    left: 12.w,
                    child: Container(
                      padding:
                          EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                      decoration: BoxDecoration(
                        color:
                            isFull ? AppPallete.red : AppPallete.secondaryColor,
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                      child: Text(
                        isFull ? 'FULL' : 'AVAILABLE',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 10.sp,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                  // Time badge
                  Positioned(
                    top: 12.h,
                    right: 12.w,
                    child: Container(
                      padding:
                          EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.7),
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                      child: Text(
                        minute.isNotEmpty ? minute : "No Time",
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 10.sp,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                  // SEN badge
                  if (sen)
                    Positioned(
                      top: 40.h,
                      right: 12.w,
                      child: Container(
                        padding: EdgeInsets.symmetric(
                            horizontal: 6.w, vertical: 2.h),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                        child: Text(
                          'SEN',
                          style: TextStyle(
                            color: AppPallete.secondaryColor,
                            fontSize: 9.sp,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
              // Content section
              Padding(
                padding: EdgeInsets.all(16.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Title and location row
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            title,
                            style: TextStyle(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w700,
                              color: Colors.black87,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        SizedBox(width: 8.w),
                        if (location == 'online')
                          online(context: context)
                        else
                          buildLocation(
                            context: context,
                            text: location,
                            color: AppPallete.scheduleColor2,
                            font: 12.sp,
                          ),
                      ],
                    ),
                    SizedBox(height: 4.h),
                    // Category and coach name
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            category,
                            style: TextStyle(
                              fontSize: 13.sp,
                              fontWeight: FontWeight.w400,
                              color: Colors.black54,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        Text(
                          time,
                          style: TextStyle(
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w600,
                            color: AppPallete.secondaryColor,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 8.h),
                    Text(
                      'by $name',
                      style: TextStyle(
                        fontSize: 13.sp,
                        fontWeight: FontWeight.w500,
                        color: Colors.black87,
                      ),
                    ),
                    SizedBox(height: 12.h),
                    // Bottom info row
                    Row(
                      children: [
                        // Student count
                        Row(
                          children: [
                            customSvgPicture(
                              imagePath: ImagePath.groupSvg,
                              height: 14.h,
                              width: 14.w,
                              color: AppPallete.secondaryColor,
                            ),
                            SizedBox(width: 4.w),
                            Text(
                              "$currentStudent/$totalStudent",
                              style: TextStyle(
                                fontSize: 13.sp,
                                fontWeight: FontWeight.w600,
                                color: Colors.black87,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(width: 16.w),
                        // Age group
                        Text(
                          "Age $ageGroup",
                          style: TextStyle(
                            fontSize: 13.sp,
                            fontWeight: FontWeight.w500,
                            color: Colors.black54,
                          ),
                        ),
                        Spacer(),
                        // Price and language
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  "HKD",
                                  style: TextStyle(
                                    fontSize: 12.sp,
                                    fontWeight: FontWeight.w600,
                                    color: AppPallete.secondaryColor,
                                  ),
                                ),
                                SizedBox(width: 4.w),
                                Text(
                                  cost,
                                  style: TextStyle(
                                    fontSize: 14.sp,
                                    fontWeight: FontWeight.w700,
                                    color: AppPallete.secondaryColor,
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 2.h),
                            Text(
                              language,
                              style: TextStyle(
                                fontSize: 11.sp,
                                fontWeight: FontWeight.w500,
                                color: Colors.black54,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    ),
  );
}

Widget online({required BuildContext context}) {
  return Row(
    children: [
      customSvgPicture(
          imagePath: ImagePath.locationSvg, height: 16.67.h, width: 11.67.w),
      SizedBox(
        width: 6.33.w,
      ),
      customtext(
          context: context,
          newYear: "online",
          font: 15.sp,
          weight: FontWeight.w400)
    ],
  );
}
