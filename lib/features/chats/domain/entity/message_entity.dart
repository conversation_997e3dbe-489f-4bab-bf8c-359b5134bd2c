import 'package:class_z/features/roles/center/data/models/center_model.dart';

class MessageEntity {
  String? message;
  BusinessCertificate? messageImage;
  DateTime? createdAt;
  String? id;

  MessageEntity({
    this.message,
    this.messageImage,
    this.createdAt,
    this.id,
  });

  // Override toString to return a string representation of the object
  @override
  String toString() {
    return 'MessageEntity{id: $id, message: $message, messageImage: $messageImage, createdAt: $createdAt}';
  }
}
