// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'chatModel.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ChatModelAdapter extends TypeAdapter<ChatModel> {
  @override
  final int typeId = 50;

  @override
  ChatModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ChatModel(
      id: fields[0] as String?,
      tempId: fields[1] as String?,
      message: fields[2] as String?,
      sender: fields[3] as String?,
      recipient: fields[4] as String?,
      senderModel: fields[5] as String?,
      recipientModel: fields[6] as String?,
      timestamp: fields[7] as DateTime?,
      status: fields[8] as String?,
      conversationId: fields[9] as String?,
      isBroadcast: fields[10] as bool?,
      imageUrl: fields[11] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, ChatModel obj) {
    writer
      ..writeByte(12)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.tempId)
      ..writeByte(2)
      ..write(obj.message)
      ..writeByte(3)
      ..write(obj.sender)
      ..writeByte(4)
      ..write(obj.recipient)
      ..writeByte(5)
      ..write(obj.senderModel)
      ..writeByte(6)
      ..write(obj.recipientModel)
      ..writeByte(7)
      ..write(obj.timestamp)
      ..writeByte(8)
      ..write(obj.status)
      ..writeByte(9)
      ..write(obj.conversationId)
      ..writeByte(10)
      ..write(obj.isBroadcast)
      ..writeByte(11)
      ..write(obj.imageUrl);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ChatModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
