import 'package:class_z/features/chats/domain/entity/message_entity.dart';
import 'package:class_z/features/roles/center/data/models/center_model.dart';

class MessageModel extends MessageEntity {
  MessageModel({
    String? message,
    BusinessCertificate? messageImage,
    DateTime? createdAt,
    String? id,
  }) : super(
          message: message,
          messageImage: messageImage,
          createdAt: createdAt,
          id: id,
        );

  factory MessageModel.fromJson(Map<String, dynamic> json) => MessageModel(
        message: json["message"],
        messageImage:
            json["messageImage"] == null || json["messageImage"] is String
                ? null
                : BusinessCertificate.fromJson(json["messageImage"]),
        createdAt: json["createdAt"] == null
            ? null
            : DateTime.parse(json["createdAt"]),
        id: json["_id"],
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "messageImage": messageImage?.toJson(),
        "createdAt": createdAt?.toIso8601String(),
        "_id": id,
      };
}
