import 'dart:convert';
import 'package:hive/hive.dart';
import 'package:hive_flutter/hive_flutter.dart';

part 'chatModel.g.dart'; // This will be generated

ChatModel chatModelFromJson(String str) => ChatModel.fromJson(json.decode(str));

String chatModelToJson(ChatModel data) => json.encode(data.toJson());

@HiveType(typeId: 50)
class ChatModel extends HiveObject {
  @HiveField(0)
  String? id;
  @HiveField(1)
  String? tempId;
  @HiveField(2)
  String? message;
  @HiveField(3)
  String? sender;
  @HiveField(4)
  String? recipient;
  @HiveField(5)
  String? senderModel;
  @HiveField(6)
  String? recipientModel;
  @HiveField(7)
  DateTime? timestamp;
  @HiveField(8)
  String? status;
  @HiveField(9)
  String? conversationId;
  @HiveField(10)
  bool? isBroadcast;
  @HiveField(11)
  String? imageUrl;

  ChatModel({
    this.id,
    this.tempId,
    this.message,
    this.sender,
    this.recipient,
    this.senderModel,
    this.recipientModel,
    this.timestamp,
    this.status,
    this.conversationId,
    this.isBroadcast,
    this.imageUrl,
  });

  // Factory constructor to create a ChatModel from JSON
  factory ChatModel.fromJson(Map<String, dynamic> json) {
    // Create conversation ID if not provided
    String? convId = json['conversationId'];
    if (convId == null && json['sender'] != null && json['recipient'] != null) {
      final List<String> users = [json['sender'], json['recipient']];
      users.sort();
      convId = users.join('-');
    }

    // Check if this is a broadcast message
    bool? isBroadcast = json['isBroadcast'] == true;

    // Special case for detecting broadcasts from conversation ID format
    if (!isBroadcast && convId != null && convId.startsWith('-')) {
      isBroadcast = true;
    }

    // Handle the ID fields with better mapping between client and server formats
    String? id = json['_id'] ?? json['id'] ?? json['dbMessageId'];
    String? tempId =
        json['tempId'] ?? json['clientMessageId'] ?? json['messageId'];

    // Determine appropriate status
    String? status = json['status'];

    // If this is a new message we're sending, start with pending
    if (status == null || status.isEmpty) {
      if (tempId != null && id == null) {
        // New outgoing message with temp ID but no server ID yet
        status = 'pending';
      } else if (id != null) {
        // Message from server should at least be "sent"
        status = 'sent';
      } else {
        // Default status
        status = 'pending';
      }
    }

    return ChatModel(
      id: id,
      tempId: tempId,
      message: json['message'],
      sender: json['sender'],
      recipient: json['recipient'],
      senderModel: json['senderModel'],
      recipientModel: json['recipientModel'],
      timestamp: json['timestamp'] != null
          ? DateTime.parse(json['timestamp'])
          : DateTime.now(),
      status: status,
      conversationId: convId,
      isBroadcast: isBroadcast,
      imageUrl: json['imageUrl'],
    );
  }

  // Method to convert ChatModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'tempId': tempId,
      'message': message,
      'sender': sender,
      'recipient': recipient,
      'senderModel': senderModel,
      'recipientModel': recipientModel,
      'timestamp': timestamp?.toIso8601String(),
      'status': status ?? 'sent',
      'conversationId': conversationId,
      'isBroadcast': isBroadcast,
      'imageUrl': imageUrl,
    };
  }

  @override
  String toString() {
    return 'ChatModel(id: $id, tempId: $tempId, message: $message, sender: $sender, recipient: $recipient, senderModel: $senderModel, recipientModel: $recipientModel, timestamp: $timestamp, conversationId: $conversationId)';
  }
}
