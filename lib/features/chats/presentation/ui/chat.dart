import 'package:class_z/core/imports.dart';
import 'package:http/http.dart' as http;
import 'package:class_z/features/chats/data/repositories/chat_local_repository.dart';
import 'dart:math' as math;

class Chat extends StatefulWidget {
  final Map<String, dynamic> data;
  const Chat({required this.data, super.key});

  @override
  State<Chat> createState() => _ChatState();
}

class _ChatState extends State<Chat> with WidgetsBindingObserver {
  final TextEditingController messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  bool isRegistered = false;
  late final String oppositeId;
  String? senderId;
  List<ChatModel> messages = [];
  late final ChatBloc _chatBloc;
  bool _isConnecting = true;
  bool _connectionFailed = false;
  String _errorMessage = "";
  bool _disposed = false;

  // Add connection timeout
  Timer? _connectionTimeoutTimer;

  // Add this property to track message history
  bool _isLoadingHistory = false;

  // Add local repository
  late final ChatLocalRepository _chatLocalRepository;
  bool _isLoadingLocalMessages = false;

  // Add a variable to track loading more messages state
  bool _isLoadingMoreMessages = false;
  String? _lastCursor;

  // Image selection for chat
  File? _selectedImage;

  @override
  void initState() {
    super.initState();

    // Register as an observer to handle keyboard events
    WidgetsBinding.instance.addObserver(this);

    try {
      // Get oppositeId from widget data or use a default value
      oppositeId = widget.data['id'] ?? '';

      // Check if senderId is provided in the widget data
      if (widget.data['senderId'] != null) {
        senderId = widget.data['senderId'];
      } else {
        // If senderId is not provided, try to get it from SharedRepository
        final sharedRepo = locator<SharedRepository>();
        senderId = sharedRepo.getUserId();

        // If still null, show an error
        if (senderId == null || senderId!.isEmpty) {
          throw Exception('Sender ID is required but not provided');
        }
      }

      // Get BLoC reference
      _chatBloc = context.read<ChatBloc>();

      // Get the local repository
      _chatLocalRepository = GetIt.instance.get<ChatLocalRepository>();

      // Debug: List all messages in storage
      _chatLocalRepository.debugListAllMessages();

      // Set a shorter connection timeout - this ensures we don't hang forever
      _connectionTimeoutTimer = Timer(const Duration(seconds: 8), () {
        if (mounted && _isConnecting && !_disposed) {
          setState(() {
            _isConnecting = false;
            _connectionFailed = true;
            _errorMessage = "Connection timeout. Please try again.";
          });
        }
      });

      // Clear messages when opening a new chat
      messages = [];

      // We'll use post-frame callback to safely initialize after the widget is built
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted && !_disposed) {
          // Check if navigating from a deep link with "/centerMessage" route
          // This could indicate a clean database state where we should skip local loading
          final bool isFromDeepLink = ModalRoute.of(context)
                  ?.settings
                  .name
                  ?.contains('/centerMessage') ==
              true;

          if (isFromDeepLink) {
            print(
                'Navigated from deep link - skipping local message load and fetching from server directly');
            // Set up listener for message confirmations
            _listenForMessageConfirmations();

            // Connect and fetch directly from server
            if (!(_chatBloc.state is ChatConnected)) {
              print("Connecting to socket - senderId: $senderId");
              _chatBloc.add(ChatConnectEvent(senderId!));
            } else {
              _fetchMessages();
            }
          } else {
            // Normal flow - load from local storage first
            _loadLocalMessages().then((_) {
              // After local messages are loaded (or not), update UI state
              if (mounted && !_disposed) {
                // Set up listener for message confirmations
                _listenForMessageConfirmations();

                // Mark messages as read
                _markMessagesAsRead();

                // Update message counts since we're viewing this chat
                _safelyRefreshMessageCount();

                // Only connect if we're not already connected
                if (!(_chatBloc.state is ChatConnected)) {
                  print("Connecting to socket - senderId: $senderId");
                  _chatBloc.add(ChatConnectEvent(senderId!));

                  // Important - set a timeout to force state transition
                  // if neither connect nor fetch completes
                  Timer(const Duration(seconds: 15), () {
                    if (mounted &&
                        !_disposed &&
                        (_isConnecting || _isLoadingHistory)) {
                      print("Force-ending loading state after timeout");
                      setState(() {
                        _isConnecting = false;
                        _isLoadingHistory = false;
                        // This will show the empty message UI if nothing else worked
                        if (messages.isEmpty) {
                          print(
                              "No messages loaded within timeout, showing empty state");
                        }
                      });
                    }
                  });
                } else {
                  print("Already connected, just fetching messages");
                  // Even if we're already connected, start fetch with timeout
                  if (!_isLoadingLocalMessages) {
                    _fetchMessages();
                  }
                }
              }
            });
          }
        }
      });
    } catch (e) {
      print("Error initializing chat: $e");
      if (mounted && !_disposed) {
        setState(() {
          _isConnecting = false;
          _connectionFailed = true;
          _errorMessage = "Failed to initialize chat: $e";
        });
      }
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Check if we need to refresh the chat when dependencies change
    final Map<String, dynamic>? args =
        ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;

    if (args != null && args != widget.data) {
      // If the chat data has changed, we need to refresh
      print("Chat data changed, refreshing...");

      // Get the new opposite ID
      final String newOppositeId = args['id'] ?? oppositeId;

      // Check if we're actually changing conversations
      if (newOppositeId != oppositeId) {
        print("Switching conversation from $oppositeId to $newOppositeId");

        // Update the conversation details
        oppositeId = newOppositeId;

        // Clear messages when switching conversations
        setState(() {
          messages = [];
        });

        // Load local messages first
        _loadLocalMessages();

        // Then fetch messages from server
        _fetchMessages();
      } else {
        print("Same conversation ID, not clearing messages");

        // Just update any other data that might have changed
        // but don't clear messages or reload
      }
    }
  }

  @override
  void dispose() {
    // Mark as disposed first to prevent setState calls
    _disposed = true;

    // Remove the observer
    WidgetsBinding.instance.removeObserver(this);

    // Cancel timers immediately
    _connectionTimeoutTimer?.cancel();

    print("Disposing Chat screen, disconnecting socket");

    try {
      // Two critical improvements:
      // 1. Only actually try to disconnect if we're connected
      // 2. Don't trigger the disconnect at all if we're already disconnecting
      if ((_chatBloc.state is ChatConnected) &&
          !(_chatBloc.state is ChatDisconnected) &&
          !(_chatBloc.state is ChatDisconnecting)) {
        // Don't wait for disconnect to complete - it happens in background
        _chatBloc.add(ChatDisconnectEvent());
      } else {
        print("Already disconnecting or disconnected, skipping disconnect");
      }

      // Make sure to clean up any message-specific resources
      messages.clear();

      // Force hide any loading dialogs that might be showing
      try {
        // This ensures any lingering loading dialogs are closed
        hideLoadingDialog(context);
      } catch (e) {
        print("Non-fatal error hiding loading dialog: $e");
      }

      // Refresh message counts when leaving the chat
      // Try-catch so failure here doesn't block disposal
      try {
        _safelyRefreshMessageCount();
      } catch (e) {
        print("Non-fatal error refreshing message count: $e");
      }
    } catch (e) {
      print("Error disconnecting chat: $e");

      // Still try to hide any loading indicators if there was an error
      try {
        hideLoadingDialog(context);
      } catch (_) {}
    }

    // Dispose controllers immediately
    messageController.dispose();
    _scrollController.dispose();

    // Call super.dispose() last
    super.dispose();
  }

  void _scrollToBottom() {
    // Improve scrolling logic to ensure it works reliably
    if (_scrollController.hasClients) {
      // Use post-frame callback to ensure UI is built before scrolling
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted && !_disposed && _scrollController.hasClients) {
          try {
            _scrollController.animateTo(
              _scrollController.position.maxScrollExtent,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeOut,
            );
            print('Scrolled to bottom of chat');
          } catch (e) {
            print('Error scrolling to bottom: $e');
          }
        }
      });
    }
  }

  // Add retry connection method
  void _retryConnection() {
    if (_disposed) return;

    // Use post-frame callback to avoid setState during build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted || _disposed) return;

      setState(() {
        _isConnecting = true;
        _connectionFailed = false;
        _errorMessage = "";
      });

      // Reset timeout timer
      _connectionTimeoutTimer?.cancel();
      _connectionTimeoutTimer = Timer(const Duration(seconds: 15), () {
        if (mounted && _isConnecting && !_disposed) {
          setState(() {
            _isConnecting = false;
            _connectionFailed = true;
            _errorMessage = "Connection timeout. Please try again.";
          });
        }
      });

      // Try to connect again with the current user ID
      if (_chatBloc.state is ChatDisconnected || _chatBloc.state is ChatError) {
        print("Attempting to reconnect socket...");
        _chatBloc.add(ChatConnectEvent(senderId!));

        // Set another timeout just for the message fetch after connection
        Timer(const Duration(seconds: 10), () {
          if (mounted &&
              !_disposed &&
              !_isLoadingHistory &&
              (_chatBloc.state is ChatConnected)) {
            print("Connected but no messages loaded yet, triggering fetch");
            _fetchMessages();
          }
        });
      } else {
        // Try fetching messages directly if already connected
        print(
            "Socket already connected, attempting to fetch messages directly...");
        _fetchMessages();
      }
    });
  }

  void _fetchMessages({bool isRetry = false}) {
    if (_disposed) return;

    // Only validate senderId - oppositeId can be empty for broadcast messages
    if (senderId == null || senderId!.isEmpty) {
      print('Cannot fetch messages: Invalid senderId. SenderId: $senderId');

      setState(() {
        _isLoadingHistory = false;
        _connectionFailed = true;
        _errorMessage = "Invalid sender ID. Please check your connection.";
      });
      return;
    }

    // Show loading indicator for message history
    setState(() {
      _isLoadingHistory = true;
    });

    // Create conversation ID based on whether oppositeId is empty
    String conversationId;
    if (oppositeId.isEmpty) {
      // For empty recipient messages, use the special format
      conversationId = '-$senderId';
      print('Using empty recipient conversation ID for fetch: $conversationId');
    } else {
      // For normal messages with a recipient
      final List<String> users = [senderId!, oppositeId];
      users.sort(); // Sort to ensure consistent ID regardless of order
      conversationId = users.join('-');
    }

    // Create a payload with the correct parameters
    final Map<String, dynamic> payload = {
      'user1': senderId!,
      // When oppositeId is empty, use senderId as user2 - server requires both user1 and user2
      'user2': oppositeId.isEmpty ? senderId : oppositeId,
      'type1': widget.data['senderType'] ?? 'user',
      'type2': oppositeId.isEmpty
          ? (widget.data['senderType'] ?? 'user')
          : (widget.data['oppositeModel'] ?? 'user'),
      'limit': 50, // Fetch 50 messages initially
      'fetchAll': true,
      'conversationId': conversationId,
      // Add a flag to indicate this is a self-conversation or broadcast
      'isBroadcast': oppositeId.isEmpty
    };

    print("Fetching messages with payload: $payload");

    // Add a timeout to handle slow responses
    Timer fetchTimeout = Timer(const Duration(seconds: 8), () {
      if (mounted && !_disposed && _isLoadingHistory) {
        print("Message history fetch taking too long, showing local messages");

        setState(() {
          _isLoadingHistory = false;
          _isConnecting = false;
        });

        // Check if we have local messages
        if (messages.isEmpty) {
          print("No messages loaded within timeout, showing empty state UI");
          _chatBloc.add(ChatEmptyMessagesEvent());
        }

        // If this is not a retry, try once more
        if (!isRetry) {
          print("Will retry fetch once after timeout");
          // Wait a short time before retrying
          Future.delayed(Duration(seconds: 2), () {
            if (!_disposed && mounted) {
              _fetchMessages(isRetry: true);
            }
          });
        }
      }
    });

    // Fetch messages with current sender and recipient IDs
    _chatBloc.add(ChatFetchMessagesEvent(payload));
  }

  @override
  Widget build(BuildContext context) {
    // Check if senderId is null and log a warning
    if (senderId == null) {
      print(
          "WARNING: Chat Screen Building with null senderId, this may cause issues");
    } else {
      print(
          "Chat Screen Building - SenderId: $senderId, OppositeId: $oppositeId");
    }

    print("Image Path: ${widget.data['imagePath']}");
    return Scaffold(
      resizeToAvoidBottomInset: true,
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildAppBar(
            name: widget.data['title'] ?? 'Chat',
            imagePath: widget.data['imagePath'] ?? '',
          ),
          Expanded(
            child: BlocConsumer<ChatBloc, ChatState>(
              listener: _blocListener,
              builder: _blocBuilder,
            ),
          ),
          _buildMessageInput(),
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildAppBar({required String name, required String imagePath}) {
    return Padding(
      padding: EdgeInsets.only(top: 70.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(left: 19.w),
            child: Row(
              children: [
                customBackButton(onTap: _navigateBack),
                const Spacer(),
                // Remove the Test Broadcast button
                const SizedBox(width: 15),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.only(top: 15.h, left: 25.w),
            child: Row(
              children: [
                const SizedBox(width: 15),
                CustomImageBuilder(
                  imagePath: imageStringGenerator(imagePath: imagePath),
                  borderRadius: 99,
                  height: 40,
                  width: 40,
                ),
                const SizedBox(width: 10),
                customtext(
                  context: context,
                  newYear: name,
                  font: 20,
                  weight: FontWeight.w500,
                ),
                const Spacer(),
                // Remove the broadcast indicator
                const SizedBox(width: 15),
              ],
            ),
          ),
          SizedBox(
            height: 16.h,
          ),
          customDivider(),
          SizedBox(
            height: 16.h,
          ),
        ],
      ),
    );
  }

  Widget _buildMessageInput() {
    // Disable the input when connection failed or still connecting
    final bool isInputEnabled = !_connectionFailed && !_isConnecting;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10.0),
      child: Column(
        children: [
          // Image preview section
          if (_selectedImage != null)
            Container(
              margin: const EdgeInsets.only(bottom: 10),
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Row(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.file(
                      _selectedImage!,
                      width: 60,
                      height: 60,
                      fit: BoxFit.cover,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Image selected',
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                            color: Colors.grey[700],
                          ),
                        ),
                        Text(
                          'Tap × to remove',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[500],
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close, color: Colors.red),
                    onPressed: _removeSelectedImage,
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                ],
              ),
            ),
          // Message input row
          Row(
            children: [
              // Image picker button with better visibility
              Container(
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(25),
                ),
                child: IconButton(
                  icon: const Icon(
                    Icons.camera_alt,
                    color: Colors.blue,
                    size: 24,
                  ),
                  onPressed: isInputEnabled ? _pickImage : null,
                  tooltip: 'Add image',
                  padding: const EdgeInsets.all(8),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: AbsorbPointer(
                  absorbing: !isInputEnabled,
                  child: Opacity(
                    opacity: isInputEnabled ? 1.0 : 0.5,
                    child: AuthField(
                      controller: messageController,
                      border: 50,
                      hintText: isInputEnabled
                          ? "Type a message..."
                          : _connectionFailed
                              ? "Connection failed"
                              : "Connecting...",
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Container(
                decoration: BoxDecoration(
                  color: _connectionFailed ? Colors.red : Colors.blue,
                  borderRadius: BorderRadius.circular(25),
                ),
                child: IconButton(
                  icon: Icon(
                    _connectionFailed ? Icons.refresh : Icons.send,
                    color: Colors.white,
                  ),
                  onPressed: _connectionFailed
                      ? _retryConnection
                      : (isInputEnabled ? _sendMessage : null),
                  padding: const EdgeInsets.all(8),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _pickImage() async {
    print('📸 Image picker button tapped in chat!');

    // Check if widget is still mounted
    if (!mounted || _disposed) {
      print('📸 Widget not mounted, cancelling image pick');
      return;
    }

    try {
      // Use a simpler approach for chat images to avoid hanging
      final ImagePicker picker = ImagePicker();

      // Show a simple picker dialog
      final ImageSource? source = await showDialog<ImageSource>(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: const Text('Select Image'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  leading: const Icon(Icons.photo_library),
                  title: const Text('Gallery'),
                  onTap: () => Navigator.of(context).pop(ImageSource.gallery),
                ),
                ListTile(
                  leading: const Icon(Icons.camera_alt),
                  title: const Text('Camera'),
                  onTap: () => Navigator.of(context).pop(ImageSource.camera),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
            ],
          );
        },
      );

      if (source == null) {
        print('📸 No source selected');
        return;
      }

      // Pick image with timeout
      final XFile? pickedFile = await picker
          .pickImage(
        source: source,
        imageQuality: 70, // Reduce quality for faster processing
        maxWidth: 1024, // Limit dimensions for chat
        maxHeight: 1024,
      )
          .timeout(
        const Duration(seconds: 15),
        onTimeout: () {
          throw Exception('Image selection timed out');
        },
      );

      // Check if widget is still mounted after async operation
      if (!mounted || _disposed) {
        print('📸 Widget disposed during image pick');
        return;
      }

      if (pickedFile != null) {
        final File imageFile = File(pickedFile.path);
        final fileSize = await imageFile.length();

        print('📸 Image selected: ${pickedFile.path}');
        print('📸 Image size: ${(fileSize / 1024).toStringAsFixed(1)}KB');

        // Check file size (max 5MB for chat)
        if (fileSize > 5 * 1024 * 1024) {
          if (mounted && !_disposed) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Image too large (max 5MB)')),
            );
          }
          return;
        }

        setState(() {
          _selectedImage = imageFile;
        });
      } else {
        print('📸 No image selected');
      }
    } catch (e) {
      print('📸 Error picking image: $e');

      // Check if widget is still mounted before showing snackbar
      if (mounted && !_disposed) {
        String errorMessage = 'Error picking image';
        if (e.toString().contains('timeout')) {
          errorMessage = 'Image selection timed out. Please try again.';
        }
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(errorMessage)),
        );
      }
    }
  }

  void _removeSelectedImage() {
    setState(() {
      _selectedImage = null;
    });
  }

  Future<Map<String, dynamic>?> _uploadImage(File imageFile) async {
    try {
      // Get the auth token
      final token = locator<SharedRepository>().getToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      print('📸 Starting image upload...');
      print('📸 File path: ${imageFile.path}');
      print('📸 File exists: ${await imageFile.exists()}');
      print('📸 File size: ${await imageFile.length()} bytes');

      // Debug token information
      print('📸 Token: ${token.substring(0, 20)}...');
      String? tokenUserId;
      try {
        // Decode JWT payload to check token structure
        final parts = token.split('.');
        if (parts.length == 3) {
          final payload =
              utf8.decode(base64Url.decode(base64Url.normalize(parts[1])));
          print('📸 Token payload: $payload');

          // Extract user ID from token
          final payloadMap = json.decode(payload);
          tokenUserId = payloadMap['id'];
          print('📸 Token user ID: $tokenUserId');
          print('📸 Chat sender ID: $senderId');
        }
      } catch (e) {
        print('📸 Error decoding token: $e');
      }

      // Create multipart request
      var request = http.MultipartRequest(
        'POST',
        Uri.parse('${AppText.device}/api/upload/image'),
      );

      // Add headers
      request.headers['auth-token'] = token;
      request.headers['Content-Type'] = 'multipart/form-data';

      // Determine content type from file extension
      String contentType = 'image/jpeg'; // default
      final extension = imageFile.path.toLowerCase();
      if (extension.endsWith('.png')) {
        contentType = 'image/png';
      } else if (extension.endsWith('.gif')) {
        contentType = 'image/gif';
      } else if (extension.endsWith('.jpg') || extension.endsWith('.jpeg')) {
        contentType = 'image/jpeg';
      }

      print('📸 Detected content type: $contentType');

      // Add the image file
      request.files.add(
        await http.MultipartFile.fromPath(
          'image',
          imageFile.path,
          contentType: MediaType.parse(contentType),
        ),
      );

      // Add image type
      request.fields['imageType'] = 'chatImage';

      print('📸 Sending upload request to: ${AppText.device}/api/upload/image');
      print('📸 Request fields: ${request.fields}');
      print('📸 Request headers: ${request.headers}');

      // Send the request with timeout
      final streamedResponse = await request.send().timeout(
        const Duration(seconds: 30),
        onTimeout: () {
          throw Exception('Upload request timed out');
        },
      );

      final response = await http.Response.fromStream(streamedResponse);

      print('📸 Upload response status: ${response.statusCode}');
      print('📸 Upload response body: ${response.body}');

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        print('📸 Upload successful: ${responseData}');
        return responseData;
      } else {
        print('📸 Upload failed with status: ${response.statusCode}');
        print('📸 Error response: ${response.body}');
        throw Exception(
            'Upload failed with status: ${response.statusCode}. Response: ${response.body}');
      }
    } catch (error) {
      print('📸 Error uploading image: $error');
      return null;
    }
  }

  void _sendMessage() async {
    if (_disposed) return;

    // Don't send empty messages unless there's an image
    if (messageController.text.trim().isEmpty && _selectedImage == null) {
      return;
    }

    try {
      // Create a temporary ID using just timestamp (this matches server expectations)
      final tempId = DateTime.now().millisecondsSinceEpoch.toString();

      // Handle image upload if there's a selected image
      String? imageUrl;
      if (_selectedImage != null) {
        try {
          final uploadResult = await _uploadImage(_selectedImage!);
          if (uploadResult != null && uploadResult['success'] == true) {
            imageUrl = uploadResult['url'];
          } else {
            throw Exception('Failed to upload image');
          }
        } catch (uploadError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Failed to upload image: $uploadError')),
          );
          return; // Don't send message if image upload fails
        }
      }

      // For broadcasts, we need special handling for recipient/conversationId
      final bool isBroadcast =
          oppositeId.isEmpty || (widget.data['isBroadcast'] == true);
      final String? conversationId = isBroadcast ? '-$senderId' : null;

      // Determine message text
      String messageText = messageController.text.trim();
      if (messageText.isEmpty && imageUrl != null) {
        messageText = "📷 Image"; // Default text for image-only messages
      }

      // Print broadcast status for debugging
      if (isBroadcast) {
        print('===== SENDING BROADCAST MESSAGE =====');
        print('Sender: $senderId');
        print('ConversationId: $conversationId');
        print('Message: $messageText');
        print('Image URL: $imageUrl');
        print('=====================================');
      } else {
        print('===== SENDING DIRECT MESSAGE =====');
        print('Sender: $senderId, Recipient: $oppositeId');
        print('Message: $messageText');
        print('Image URL: $imageUrl');
        print('===================================');
      }

      // Build the message payload
      final message = {
        'message': messageText,
        'sender': senderId,
        // For broadcast, use either empty string or the sender as recipient
        'recipient': isBroadcast ? senderId : oppositeId,
        'senderModel': widget.data['senderType'] ?? 'user',
        // For broadcast, use the sender's own model type or default to 'user'
        'recipientModel': isBroadcast
            ? (widget.data['senderType'] ?? 'user')
            : (widget.data['oppositeModel'] ?? 'user'),
        'timestamp': _getSafeTimestamp(),
        'tempId': tempId,
        'status': 'pending', // Start with pending status
        'conversationId': conversationId,
        'isBroadcast':
            isBroadcast, // Make sure the broadcast flag is set properly
        // Add image data if available
        if (imageUrl != null) 'imageUrl': imageUrl,
      };

      // Create message model for local display
      final messageModel = ChatModel.fromJson(message);

      // Add the message to the UI immediately for responsiveness
      setState(() {
        messages.add(messageModel);
        // Clear the input field and selected image
        messageController.clear();
        _selectedImage = null;
      });

      // Debug print message IDs after adding
      print(
          'Added message with tempId: $tempId, message ID: ${messageModel.id}');
      print('Total messages count: ${messages.length}');

      // Save to local storage right away
      _saveMessageLocally(messageModel);

      // IMPORTANT: Make sure to scroll to bottom after sending
      _scrollToBottom();

      // Send the message through the bloc
      _chatBloc.add(ChatSendMessageEvent(message));

      // Set a timeout to update status to "sent" if no confirmation after 3 seconds
      // This ensures the user sees progress even if server confirmation is delayed
      Timer(const Duration(seconds: 3), () {
        if (mounted && !_disposed) {
          // Find the message with this tempId
          final index = messages.indexWhere((msg) => msg.tempId == tempId);
          if (index != -1) {
            // Only update if status is still pending
            if (messages[index].status == 'pending') {
              print(
                  'Auto-updating message status to "sent" after timeout: $tempId');
              setState(() {
                messages[index].status = 'sent';
                // Update in local storage too
                _saveMessageLocally(messages[index]);
              });
            }
          }
        }
      });

      // For broadcast messages, set another timeout to update to "delivered" after 6 seconds
      // This gives visual feedback even if the server doesn't confirm delivery
      if (isBroadcast) {
        Timer(const Duration(seconds: 6), () {
          if (mounted && !_disposed) {
            // Find the message with this tempId
            final index = messages.indexWhere((msg) => msg.tempId == tempId);
            if (index != -1) {
              // Only update if status is still sent
              if (messages[index].status == 'sent') {
                print(
                    'Auto-updating broadcast message status to "delivered" after timeout: $tempId');
                setState(() {
                  messages[index].status = 'delivered';
                  // Update in local storage too
                  _saveMessageLocally(messages[index]);
                });
              }
            }
          }
        });
      }
    } catch (e) {
      // Show error
      print('Error sending message: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to send message: $e')),
      );
    }
  }

  // Helper method to generate random string for message IDs
  String _generateRandomString(int length) {
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    final random = math.Random();
    return String.fromCharCodes(Iterable.generate(
        length, (_) => chars.codeUnitAt(random.nextInt(chars.length))));
  }

  // Improved duplicate message detection function
  bool _isDuplicateMessage(ChatModel newMessage) {
    // Skip null or empty messages
    if (newMessage.message == null || newMessage.message!.isEmpty) {
      return true;
    }

    // First check for exact ID match if IDs are available
    if (newMessage.id != null) {
      for (final message in messages) {
        if (message.id == newMessage.id) {
          print(
              'Duplicate detected: Message with ID ${newMessage.id} already exists');
          return true;
        }
      }
    }

    // Check for tempId match
    if (newMessage.tempId != null) {
      for (final message in messages) {
        if (message.tempId == newMessage.tempId) {
          print(
              'Duplicate detected: Message with tempID ${newMessage.tempId} already exists');
          // Don't try to directly modify the ID here, just report it as a duplicate
          // The update will happen in _handleIncomingMessage
          return true;
        }
      }
    }

    // For broadcast messages, do additional checks
    final bool isBroadcast = newMessage.isBroadcast == true ||
        (newMessage.conversationId != null &&
            newMessage.conversationId!.startsWith('-'));

    if (isBroadcast) {
      // For broadcasts, check content + sender + timestamp (more strict)
      for (final message in messages) {
        if (message.message == newMessage.message &&
            message.sender == newMessage.sender) {
          // Check timestamp if available (use a larger window for broadcasts)
          if (message.timestamp != null && newMessage.timestamp != null) {
            // For broadcasts, use a 5-second window because there might be network delays
            final timeDifference = message.timestamp!
                .difference(newMessage.timestamp!)
                .inSeconds
                .abs();
            if (timeDifference < 5) {
              print(
                  'Duplicate broadcast message detected based on content and timestamp proximity');
              return true;
            }
          } else if (message.message == newMessage.message) {
            // If no timestamp but content matches, assume duplicate for broadcasts
            print(
                'Duplicate broadcast message detected based on content (no timestamp)');
            return true;
          }
        }
      }
      return false; // Not a duplicate broadcast
    }

    // For regular messages, check content + sender + recipient + timestamp match (within 2 seconds)
    for (final message in messages) {
      if (message.message == newMessage.message &&
          message.sender == newMessage.sender &&
          message.recipient == newMessage.recipient) {
        // Check timestamp if available
        if (message.timestamp != null && newMessage.timestamp != null) {
          // If messages are within 2 seconds of each other, consider them duplicates
          final timeDifference = message.timestamp!
              .difference(newMessage.timestamp!)
              .inSeconds
              .abs();
          if (timeDifference < 2) {
            print(
                'Duplicate detected based on content and timestamp proximity');
            return true;
          }
        } else {
          // If no timestamps, consider it a duplicate based on content
          print('Duplicate detected based on content (no timestamp)');
          return true;
        }
      }
    }

    return false;
  }

  Widget _blocBuilder(BuildContext context, ChatState state) {
    print("Current chat state: $state");

    // Handle different connection states
    if (state is ChatLoading) {
      return _buildLoadingView('Connecting...');
    } else if (state is ChatConnected) {
      if (_isLoadingHistory) {
        return _buildLoadingView('Loading messages...');
      } else if (messages.isEmpty) {
        return _buildEmptyView();
      } else {
        return _buildMessageList();
      }
    } else if (state is ChatMessagesLoaded) {
      // Just render the current messages - state changes will be handled in the listener
      return messages.isEmpty ? _buildEmptyView() : _buildMessageList();
    } else if (state is ChatMessageReceived) {
      // Just render the current messages - state changes will be handled in the listener
      return messages.isEmpty ? _buildEmptyView() : _buildMessageList();
    } else if (state is ChatError) {
      // Log the error and show appropriate UI
      return _buildErrorView(state.error);
    } else if (state is ChatDisconnected) {
      if (_connectionFailed) {
        return _buildConnectionFailedView();
      } else if (messages.isEmpty) {
        return _buildEmptyView();
      } else {
        return _buildMessageList();
      }
    } else {
      // Default case
      if (_connectionFailed) {
        return _buildConnectionFailedView();
      } else if (_isConnecting) {
        return _buildLoadingView('Initializing...');
      } else if (messages.isEmpty) {
        return _buildEmptyView();
      } else {
        return _buildMessageList();
      }
    }
  }

  void _blocListener(BuildContext context, ChatState state) {
    if (_disposed) return; // Safety check

    print('Chat bloc state change: $state');

    if (state is ChatError) {
      print('Chat error: ${state.error}');

      // Update state safely using post-frame callback to avoid conflicts with build
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted && !_disposed) {
          setState(() {
            _isConnecting = false;
            _connectionFailed = true;
            _errorMessage = state.error;
          });

          // If there's a database error and we have cached messages, clear the cache
          if (state.error.contains('database') ||
              state.error.contains('fetch') ||
              state.error.contains('connection')) {
            print('Database error detected, clearing local message cache');
            _clearLocalMessageCache();
          }

          // Show a snackbar with the error only if it's not a parsing error
          if (!state.error.contains('parse') &&
              !state.error.contains('format')) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.error),
                duration: const Duration(seconds: 3),
              ),
            );
          }
        }
      });
    }

    if (state is ChatConnected) {
      print('Chat connected! Fetching messages...');

      // Update state safely using post-frame callback
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted && !_disposed) {
          // Cancel timeout timer since we're connected
          _connectionTimeoutTimer?.cancel();

          setState(() {
            _connectionFailed = false;
            _isConnecting = false;

            // Update all pending messages to at least "sent" now that we're connected
            for (int i = 0; i < messages.length; i++) {
              if (messages[i].sender == senderId &&
                  (messages[i].status == null ||
                      messages[i].status == 'pending')) {
                messages[i].status = 'sent';
                // Save updates to local storage
                _saveMessageLocally(messages[i]);
              }
            }
          });

          // Print updated message statuses
          _debugPrintMessageStatuses();

          // Only fetch messages if we don't already have messages for this conversation
          // or if we're explicitly loading local messages
          if (messages.isEmpty || _isLoadingLocalMessages) {
            print("No messages loaded yet, fetching from server");
            _fetchMessages();
          } else {
            print(
                "Already have ${messages.length} messages, not fetching from server");
          }
        }
      });
    }

    if (state is ChatMessageReceived) {
      // Use the improved _handleIncomingMessage method to process new messages
      final newMessage = state.message;

      // Additional logging for debug purpose
      if (oppositeId.isEmpty) {
        print(
            'Processing message for empty recipient chat: ${newMessage.sender} -> ${newMessage.recipient}');
      }

      // Do a quick check if this message is relevant to us
      if (_isMessageForCurrentChat(newMessage)) {
        print('Message is relevant to current chat, processing');
        _handleIncomingMessage(newMessage);
      } else {
        print(
            'Ignoring message not for this conversation - sender: ${newMessage.sender}, recipient: ${newMessage.recipient}');
      }
    }

    // Add handling for message status updates
    if (state is ChatMessageStatusUpdated) {
      // Find the message by ID and update its status
      final messageId = state.messageId;
      final status = state.status;

      print('Received status update from bloc: $messageId -> $status');

      final index = messages.indexWhere((msg) => msg.id == messageId);
      if (index != -1) {
        // Only update if status has actually changed
        if (messages[index].status != status) {
          setState(() {
            messages[index].status = status;
            // Update in local storage
            _saveMessageLocally(messages[index]);
          });
          print(
              'Updated message status from bloc listener: $messageId -> $status');
        }
      }
    }

    if (state is ChatMessagesLoaded) {
      // Handle message loading in a post-frame callback
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (!mounted || _disposed) return;

        setState(() {
          // Update loading states
          _isLoadingHistory = false;

          // If we're loading more (pagination) and get an empty response, just mark pagination done
          if (state.messages.isEmpty && _isLoadingMoreMessages) {
            print('No more older messages available - end of history reached');
            _isLoadingMoreMessages = false;
          }
          // If server returned empty list for initial load, clear the cache
          else if (state.messages.isEmpty &&
              messages.isNotEmpty &&
              !_isLoadingMoreMessages) {
            print(
                'Server returned no messages but we have cached messages - clearing cache');
            _clearLocalMessageCache();
          }
          // If messages is empty but we have loaded messages, use them
          else if (messages.isEmpty && state.messages.isNotEmpty) {
            messages = [...state.messages];
            print('Set ${messages.length} messages from server');
            _isLoadingMoreMessages = false;
          } else if (state.messages.isNotEmpty) {
            // If we're loading more messages, prepend them to the list
            if (_isLoadingMoreMessages) {
              print(
                  'Adding ${state.messages.length} older messages to the existing ${messages.length} messages');
              _processOlderMessages(state.messages);
              _isLoadingMoreMessages = false;
            } else {
              // Otherwise, replace current messages
              print('Processing ${state.messages.length} loaded messages');
              _processMessages(state.messages);
            }
          } else {
            print('No messages received from server');
            _isLoadingMoreMessages = false;
          }
        });

        // Mark any unread messages as read if there are any messages
        if (messages.isNotEmpty) {
          _markMessagesAsRead();
        }

        // Scroll to bottom if we have messages
        if (messages.isNotEmpty) {
          _scrollToBottom();
        }
      });
    }
  }

  Widget _buildMessageList() {
    if (_isLoadingHistory) {
      return const Center(child: CircularProgressIndicator());
    }

    if (messages.isEmpty) {
      return Center(child: Text('No messages yet. Start a conversation!'));
    }

    // First, deduplicate messages in the UI
    final Set<String> seenMessageKeys = {};
    final List<ChatModel> uniqueMessages = [];

    // Sort by timestamp first
    final sortedMessages = List<ChatModel>.from(messages);
    sortedMessages.sort((a, b) => (a.timestamp ?? DateTime.now())
        .compareTo(b.timestamp ?? DateTime.now()));

    // Then deduplicate
    for (final message in sortedMessages) {
      String key;
      // Use id if available, otherwise use tempId or other unique combination
      if (message.id != null) {
        key = "id:${message.id}";
      } else if (message.tempId != null) {
        key = "tempId:${message.tempId}";
      } else {
        // Create a content-based key
        key =
            "${message.sender}_${message.message}_${message.timestamp?.millisecondsSinceEpoch}";
      }

      if (!seenMessageKeys.contains(key)) {
        seenMessageKeys.add(key);
        uniqueMessages.add(message);
      }
    }

    return NotificationListener<ScrollNotification>(
      onNotification: (ScrollNotification scrollInfo) {
        // Detect when user scrolls to the top and not already loading more
        if (scrollInfo.metrics.pixels <=
                scrollInfo.metrics.minScrollExtent + 10 &&
            !_isLoadingMoreMessages &&
            !_isLoadingHistory) {
          _loadMoreMessages();
        }
        return true;
      },
      child: ListView.builder(
        controller: _scrollController,
        itemCount:
            uniqueMessages.length, // Use uniqueMessages instead of messages
        itemBuilder: (context, index) => _buildMessageItem(
            context, index, uniqueMessages), // Pass uniqueMessages
      ),
    );
  }

  // Update _buildMessageItem to accept the message list
  Widget _buildMessageItem(
      BuildContext context, int index, List<ChatModel> messageList) {
    final message = messageList[index];
    final isSender = message.sender == senderId;

    // Determine if we should show the date header
    bool showDateHeader = false;
    final DateTime messageDate = message.timestamp ?? DateTime.now();

    if (index == 0) {
      showDateHeader = true;
    } else {
      final previousMessage = messageList[index - 1];
      final DateTime previousDate = previousMessage.timestamp ?? DateTime.now();

      // Show date header if the date is different from the previous message
      if (!_isSameDay(previousDate, messageDate)) {
        showDateHeader = true;
      }
    }

    return Column(
      children: [
        // Show date header if needed
        if (showDateHeader)
          Padding(
            padding: EdgeInsets.symmetric(vertical: 10.h),
            child: Center(
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  _formatDate(messageDate),
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Colors.grey[700],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ),

        // Message bubble
        Align(
          alignment: isSender ? Alignment.centerRight : Alignment.centerLeft,
          child: Container(
            constraints: BoxConstraints(maxWidth: 270.w),
            margin: EdgeInsets.symmetric(vertical: 5.h, horizontal: 10.w),
            padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 10.h),
            decoration: BoxDecoration(
              color: isSender ? AppPallete.secondaryColor : Colors.grey[200],
              borderRadius: BorderRadius.circular(18),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  spreadRadius: 1,
                  blurRadius: 1,
                  offset: Offset(0, 1),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Display image if available
                if (message.imageUrl != null && message.imageUrl!.isNotEmpty)
                  Container(
                    margin: EdgeInsets.only(bottom: 8.h),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: _buildChatMessageImage(message.imageUrl!),
                    ),
                  ),
                // Display text message (only if not empty)
                if (message.message != null && message.message!.isNotEmpty)
                  Text(
                    message.message!,
                    style: TextStyle(
                      fontSize: 16.h,
                      color: isSender ? Colors.white : Colors.black87,
                    ),
                  ),
                SizedBox(height: 6.h),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Text(
                      _formatTime(message.timestamp),
                      style: TextStyle(
                        fontSize: 10.sp,
                        color: isSender ? Colors.white70 : Colors.grey[700],
                      ),
                    ),
                    SizedBox(width: 5.w),
                    // Add status indicator for sent messages with subtle animation
                    if (isSender)
                      AnimatedSwitcher(
                        duration: Duration(milliseconds: 300),
                        child: _buildMessageStatus(message),
                      ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildChatMessageImage(String imageUrl) {
    // Check if it's a network URL or local file path
    if (imageUrl.startsWith('http') || imageUrl.startsWith('/uploads/')) {
      // Network image
      String fullUrl =
          imageUrl.startsWith('http') ? imageUrl : '${AppText.device}$imageUrl';

      return Image.network(
        fullUrl,
        width: 200.w,
        height: 150.h,
        fit: BoxFit.cover,
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return Container(
            width: 200.w,
            height: 150.h,
            color: Colors.grey[200],
            child: Center(
              child: CircularProgressIndicator(
                value: loadingProgress.expectedTotalBytes != null
                    ? loadingProgress.cumulativeBytesLoaded /
                        loadingProgress.expectedTotalBytes!
                    : null,
              ),
            ),
          );
        },
        errorBuilder: (context, error, stackTrace) {
          return Container(
            width: 200.w,
            height: 150.h,
            color: Colors.grey[300],
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.broken_image,
                  color: Colors.grey,
                  size: 50.sp,
                ),
                Text(
                  'Failed to load image',
                  style: TextStyle(color: Colors.grey, fontSize: 12.sp),
                ),
              ],
            ),
          );
        },
      );
    } else {
      // Local file
      return Image.file(
        File(imageUrl),
        width: 200.w,
        height: 150.h,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            width: 200.w,
            height: 150.h,
            color: Colors.grey[300],
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.broken_image,
                  color: Colors.grey,
                  size: 50.sp,
                ),
                Text(
                  'Image not found',
                  style: TextStyle(color: Colors.grey, fontSize: 12.sp),
                ),
              ],
            ),
          );
        },
      );
    }
  }

  Widget _buildMessageBubble(BuildContext context, ChatModel message) {
    try {
      // Handle null senderId gracefully
      final isSentByUser = senderId != null && message.sender == senderId;

      // Validate data
      final senderImage = widget.data['senderImage']?.toString() ?? '';
      final receiverImage = widget.data['imagePath']?.toString() ?? '';
      final messageText = message.message ?? '';

      // Format timestamp
      final String timeString = message.timestamp != null
          ? DateFormat('h:mm a').format(message.timestamp!)
          : '';

      return Row(
        mainAxisAlignment:
            isSentByUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: isSentByUser
            ? [
                // Message bubble with timestamp
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Container(
                      constraints: BoxConstraints(maxWidth: 275.w),
                      margin: const EdgeInsets.symmetric(vertical: 5),
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        color: AppPallete.secondaryColor,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: customtext(
                        context: context,
                        newYear: messageText,
                        font: 15.sp,
                        weight: FontWeight.w400,
                        color: AppPallete.white,
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(right: 8.0),
                      child: Text(
                        timeString,
                        style: TextStyle(
                          fontSize: 10.sp,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(width: 8),
                // Sender Image
                CustomImageBuilder(
                  borderRadius: 37.w,
                  imagePath: imageStringGenerator(imagePath: senderImage),
                  height: 37.h,
                  width: 37.w,
                ),
              ]
            : [
                // Receiver Image
                CustomImageBuilder(
                  borderRadius: 37.w,
                  imagePath: imageStringGenerator(imagePath: receiverImage),
                  height: 37.h,
                  width: 37.w,
                ),
                const SizedBox(width: 8),
                // Message bubble with timestamp
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      constraints: BoxConstraints(maxWidth: 275.w),
                      margin: const EdgeInsets.symmetric(vertical: 5),
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        color: const Color(0xFFF1F1F1),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: customtext(
                        context: context,
                        newYear: messageText,
                        font: 15.sp,
                        weight: FontWeight.w400,
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 8.0),
                      child: Text(
                        timeString,
                        style: TextStyle(
                          fontSize: 10.sp,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
      );
    } catch (e) {
      print('Error rendering message: $e');
      return const SizedBox.shrink(); // Skip rendering this message
    }
  }

  // Ensure BadgeCountService is available
  void _ensureBadgeServiceAvailable() {
    if (_disposed || !mounted) return;

    try {
      final locatorInstance = GetIt.instance;

      // If BadgeCountService is not registered, create it on demand
      if (!locatorInstance.isRegistered<BadgeCountService>()) {
        print('BadgeCountService not found, registering it now');
        // Register it as a singleton
        locatorInstance.registerLazySingleton<BadgeCountService>(
            () => BadgeCountService());
      }

      // Make sure it's initialized with the user ID
      if (senderId != null && senderId!.isNotEmpty) {
        final badgeService = locatorInstance.get<BadgeCountService>();
        // Force initialization if needed
        if (badgeService.notificationCount == 0 &&
            badgeService.messageCount == 0) {
          badgeService.initialize(senderId!);
        }
      }
    } catch (e) {
      print('Error ensuring BadgeCountService availability: $e');
    }
  }

  // Safely refresh message count, handling cases where the service isn't available
  void _safelyRefreshMessageCount() {
    try {
      // Only update badge count if we have a valid chat screen
      if (_disposed || !mounted || senderId == null || senderId!.isEmpty) {
        return;
      }

      // Check if we have messages in the chat
      bool hasMessagesInChat = messages.isNotEmpty;

      // Get the badge service
      final badgeService = GetIt.I<BadgeCountService>();
      final userId = senderId;

      if (userId != null && userId.isNotEmpty) {
        if (hasMessagesInChat) {
          // If we have messages in the chat UI, make sure badge count isn't zero
          if (badgeService.messageCount == 0) {
            badgeService.setMessageCount(1);
          } else {
            // Normal refresh (will check local storage now)
            badgeService.refreshMessageCount(userId);
          }
        } else {
          // If no messages in UI, still refresh but from local storage
          badgeService.refreshMessageCount(userId);
        }
      }
    } catch (e) {
      print("Error refreshing message count: $e");
    }
  }

  void _navigateBack() {
    if (_disposed) return;

    // Ensure loading dialogs are closed before navigating back
    try {
      hideLoadingDialog(context);
    } catch (_) {}

    // Navigate back safely
    Navigator.of(context).pop();
  }

  // Add method to load messages from local storage
  Future<void> _loadLocalMessages() async {
    if (_disposed) return;

    setState(() {
      _isLoadingLocalMessages = true;
    });

    try {
      // Create a consistent conversation ID by sorting user IDs
      final List<String> users = [senderId!, oppositeId];
      users.sort(); // Sort to ensure consistent ID regardless of order
      final String conversationId = users.join('-');

      print(
          'Loading messages from local storage for conversation: $conversationId');

      // Get the local repository safely
      ChatLocalRepository? repository;
      try {
        repository = GetIt.instance.get<ChatLocalRepository>();
      } catch (e) {
        print('Error getting ChatLocalRepository: $e');
        setState(() {
          _isLoadingLocalMessages = false;
        });
        return;
      }

      // Initialize repository if needed
      try {
        await repository.initialize();
      } catch (e) {
        print('Error initializing repository: $e');
      }

      // Get messages for this conversation using the new loadMessages method
      final localMessages =
          await repository.loadMessages(senderId!, oppositeId);

      if (mounted && !_disposed) {
        if (localMessages.isNotEmpty) {
          setState(() {
            messages = localMessages;
            _isLoadingLocalMessages = false;
          });
          print('Loaded ${localMessages.length} messages from local storage');

          // Log the first and last message for debugging
          print(
              'First message: ${localMessages.first.message} from ${localMessages.first.sender} to ${localMessages.first.recipient}');
          print(
              'Last message: ${localMessages.last.message} from ${localMessages.last.sender} to ${localMessages.last.recipient}');
        } else {
          setState(() {
            messages = [];
            _isLoadingLocalMessages = false;
          });
          print('No messages found in local storage');
        }
      }
    } catch (e) {
      if (mounted && !_disposed) {
        setState(() {
          _isLoadingLocalMessages = false;
        });
      }
      print('Error loading messages from local storage: $e');
    }
  }

  // Helper method to merge messages from server with local messages
  List<ChatModel> _mergeMessages(
      List<ChatModel> localMessages, List<ChatModel> serverMessages) {
    print(
        'Merging ${localMessages.length} local messages with ${serverMessages.length} server messages');

    // Print sample messages for debugging
    if (localMessages.isNotEmpty) {
      print(
          'Sample local message: ${localMessages.first.message} from ${localMessages.first.sender} to ${localMessages.first.recipient}');
    }
    if (serverMessages.isNotEmpty) {
      print(
          'Sample server message: ${serverMessages.first.message} from ${serverMessages.first.sender} to ${serverMessages.first.recipient}');
    }

    // Create a map to track messages by their unique identifiers
    final Map<String, ChatModel> mergedMap = {};

    // Add all local messages to the map
    for (final message in localMessages) {
      final key = message.id ??
          message.tempId ??
          '${message.sender}-${message.recipient}-${message.message}-${message.timestamp?.millisecondsSinceEpoch}';
      mergedMap[key] = message;
    }

    print('Added ${mergedMap.length} local messages to merge map');

    // Add or update with server messages (server messages take precedence)
    int updatedCount = 0;
    int addedCount = 0;

    for (final message in serverMessages) {
      String key;
      bool isUpdate = false;

      // Try to find by ID first
      if (message.id != null) {
        key = message.id!;
        isUpdate = mergedMap.containsKey(key);
        mergedMap[key] = message;
        if (isUpdate)
          updatedCount++;
        else
          addedCount++;
        continue;
      }

      // Try to find by tempId
      if (message.tempId != null) {
        key = message.tempId!;
        isUpdate = mergedMap.containsKey(key);
        mergedMap[key] = message;
        if (isUpdate)
          updatedCount++;
        else
          addedCount++;
        continue;
      }

      // Fall back to content-based key
      key =
          '${message.sender}-${message.recipient}-${message.message}-${message.timestamp?.millisecondsSinceEpoch}';
      isUpdate = mergedMap.containsKey(key);
      mergedMap[key] = message;
      if (isUpdate)
        updatedCount++;
      else
        addedCount++;
    }

    print(
        'Updated $updatedCount existing messages and added $addedCount new messages from server');

    // Convert back to list and sort by timestamp
    final result = mergedMap.values.toList();
    result.sort((a, b) => (a.timestamp ?? DateTime.now())
        .compareTo(b.timestamp ?? DateTime.now()));

    print(
        'Merged ${localMessages.length} local messages with ${serverMessages.length} server messages, resulting in ${result.length} unique messages');

    // Print first and last message for debugging
    if (result.isNotEmpty) {
      print(
          'First merged message: ${result.first.message} from ${result.first.sender} to ${result.first.recipient}');
      print(
          'Last merged message: ${result.last.message} from ${result.last.sender} to ${result.last.recipient}');
    }

    return result;
  }

  // Debug method to print the current messages
  void _debugPrintMessages() {
    print('===== DEBUG: CURRENT MESSAGES IN CHAT =====');
    print('Total messages in chat: ${messages.length}');

    if (messages.isEmpty) {
      print('No messages to display');
    } else {
      // Group messages by sender
      final Map<String, List<ChatModel>> messagesBySender = {};

      for (final message in messages) {
        final sender = message.sender ?? 'unknown';

        if (!messagesBySender.containsKey(sender)) {
          messagesBySender[sender] = [];
        }

        messagesBySender[sender]!.add(message);
      }

      // Print messages grouped by sender
      messagesBySender.forEach((sender, senderMessages) {
        print('Sender: $sender - ${senderMessages.length} messages');

        // Print first and last message if there are any
        if (senderMessages.isNotEmpty) {
          final firstMessage = senderMessages.first;
          final lastMessage = senderMessages.last;

          print(
              '  First: "${firstMessage.message}" to ${firstMessage.recipient} at ${firstMessage.timestamp}');

          if (senderMessages.length > 1) {
            print(
                '  Last: "${lastMessage.message}" to ${lastMessage.recipient} at ${lastMessage.timestamp}');
          }
        }
      });
    }

    print('===== END DEBUG MESSAGES =====');
  }

  // New helper method to handle incoming messages
  void _handleIncomingMessage(ChatModel newMessage) {
    // Skip processing if disposed
    if (_disposed) return;

    // Check if this is a broadcast message using a comprehensive check
    final bool isBroadcast = newMessage.isBroadcast == true ||
        (newMessage.conversationId != null &&
            newMessage.conversationId!.startsWith('-')) ||
        (newMessage.sender == newMessage.recipient &&
            newMessage.sender != null);

    // Log message type for debugging
    if (isBroadcast) {
      print('💬📢 BROADCAST MESSAGE RECEIVED:');
      print(
          '  ID: ${newMessage.id ?? 'null'}, TempID: ${newMessage.tempId ?? 'null'}');
      print(
          '  Sender: ${newMessage.sender}, Recipient: ${newMessage.recipient}');
      print('  ConversationId: ${newMessage.conversationId ?? 'null'}');
      print(
          '  Status: ${newMessage.status ?? 'null'}, isBroadcast flag: ${newMessage.isBroadcast}');
      print(
          '  Message: ${newMessage.message?.substring(0, math.min(30, newMessage.message?.length ?? 0))}${newMessage.message != null && newMessage.message!.length > 30 ? '...' : ''}');
    } else {
      print('💬 DIRECT MESSAGE RECEIVED:');
      print(
          '  ID: ${newMessage.id ?? 'null'}, TempID: ${newMessage.tempId ?? 'null'}');
      print(
          '  Sender: ${newMessage.sender}, Recipient: ${newMessage.recipient}');
      print('  Status: ${newMessage.status ?? 'null'}');
    }

    try {
      // Ensure the message has the correct broadcast flag
      if (isBroadcast && newMessage.isBroadcast != true) {
        print('Setting missing isBroadcast flag on message');
        newMessage.isBroadcast = true;
      }

      // Check if this is a message ID mapping update (has both IDs but might be missing content)
      if (newMessage.id != null &&
          newMessage.tempId != null &&
          (newMessage.message == null || newMessage.message!.isEmpty)) {
        print(
            'Received ID mapping update: tempId=${newMessage.tempId} to serverId=${newMessage.id}');

        // Find the message in our list by tempId and update its server ID
        final index =
            messages.indexWhere((msg) => msg.tempId == newMessage.tempId);
        if (index != -1) {
          setState(() {
            messages[index].id = newMessage.id;
            if (newMessage.status != null) {
              messages[index].status = newMessage.status;
            }

            // Update the isBroadcast flag if needed
            if (isBroadcast) {
              messages[index].isBroadcast = true;
            }

            // Update in local storage too
            _saveMessageLocally(messages[index]);
          });
          print(
              'Updated message ID mapping in UI: tempId=${newMessage.tempId} -> serverId=${newMessage.id}');
          return;
        }
      }

      // Check if this is a status update (has ID and status but might be missing other fields)
      if (newMessage.id != null &&
          newMessage.status != null &&
          (newMessage.message == null || newMessage.message!.isEmpty)) {
        print(
            'Received status update for message ${newMessage.id}: ${newMessage.status}');

        // Find the message in our list and update its status
        final index = messages.indexWhere((msg) => msg.id == newMessage.id);
        if (index != -1) {
          setState(() {
            // Don't update if status hasn't changed (prevents unnecessary rebuilds)
            if (messages[index].status != newMessage.status) {
              messages[index].status = newMessage.status;

              // Update the isBroadcast flag if needed
              if (isBroadcast) {
                messages[index].isBroadcast = true;
              }

              // Update in local storage too
              _saveMessageLocally(messages[index]);
            }
          });
          print(
              'Updated message status in UI: id=${newMessage.id}, status=${newMessage.status}');
          return;
        }

        // Also check if this is a tempId confirmation
        final tempIndex =
            messages.indexWhere((msg) => msg.tempId == newMessage.tempId);
        if (tempIndex != -1) {
          setState(() {
            if (newMessage.id != null) {
              messages[tempIndex].id = newMessage.id;
            }

            // Don't update if status hasn't changed (prevents unnecessary rebuilds)
            if (messages[tempIndex].status != newMessage.status) {
              messages[tempIndex].status = newMessage.status;
            }

            // Update the isBroadcast flag if needed
            if (isBroadcast) {
              messages[tempIndex].isBroadcast = true;
            }

            // Update in local storage
            _saveMessageLocally(messages[tempIndex]);
          });
          print(
              'Updated message with tempId=${newMessage.tempId} status to ${newMessage.status}');
          return;
        }

        // If we didn't find the message, just ignore the status update
        return;
      }

      // CRITICAL: Check if duplicate before proceeding with a normal message
      if (_isDuplicateMessage(newMessage)) {
        print(
            'Skipping duplicate message: ${newMessage.message?.substring(0, math.min(30, newMessage.message?.length ?? 0))}${newMessage.message != null && newMessage.message!.length > 30 ? '...' : ''}');

        // If this is a confirmation with server ID for a message we already have by tempId
        if (newMessage.id != null && newMessage.tempId != null) {
          final index =
              messages.indexWhere((msg) => msg.tempId == newMessage.tempId);
          if (index != -1 && messages[index].id == null) {
            // Update the existing message with the server ID and status
            setState(() {
              messages[index].id = newMessage.id;
              messages[index].status = newMessage.status ?? 'sent';

              // Update the isBroadcast flag if needed
              if (isBroadcast) {
                messages[index].isBroadcast = true;
              }

              // Update in local storage
              _saveMessageLocally(messages[index]);
            });
            print('Updated existing message with server ID: ${newMessage.id}');
          }
        }
        return;
      }

      // Check if this is a broadcast message for the current user chat
      if (isBroadcast) {
        // Check if we're in the broadcast view
        final bool isInBroadcastView =
            oppositeId.isEmpty || (widget.data['isBroadcast'] == true);

        if (!isInBroadcastView) {
          print(
              'Received broadcast message while in direct chat - saving but not displaying');
          // Save to local storage but don't add to UI if we're not in broadcast view
          _saveMessageLocally(newMessage);
          return;
        }
      }
      // For direct messages, check that they belong to this conversation
      else if (oppositeId.isNotEmpty) {
        final bool isForThisConversation = (newMessage.sender == oppositeId &&
                newMessage.recipient == senderId) ||
            (newMessage.sender == senderId &&
                newMessage.recipient == oppositeId);

        if (!isForThisConversation) {
          print(
              'Received direct message not for this conversation - saving but not displaying');
          // Save to local storage but don't add to UI
          _saveMessageLocally(newMessage);
          return;
        }
      }

      // Validate timestamp - ensure it's not in the future
      if (newMessage.timestamp != null) {
        final now = DateTime.now();
        if (newMessage.timestamp!
            .isAfter(now.add(const Duration(seconds: 1)))) {
          print(
              'Warning: Incoming message has future timestamp (${newMessage.timestamp!.toIso8601String()}), adjusting to current time');
          newMessage.timestamp = now;
        }
      } else {
        // If no timestamp provided, set to current time
        newMessage.timestamp = DateTime.now();
        print('No timestamp on incoming message, setting to current time');
      }

      // Add this new message to our list
      setState(() {
        // Set default status based on sender
        if (newMessage.status == null) {
          newMessage.status =
              (newMessage.sender == senderId) ? 'sent' : 'delivered';
        }

        messages.add(newMessage);
        messages.sort((a, b) => (a.timestamp ?? DateTime.now())
            .compareTo(b.timestamp ?? DateTime.now()));

        print('Added new message to UI. Total count: ${messages.length}');
      });

      // IMPORTANT: Always scroll to bottom when a new message is added
      _scrollToBottom();

      // Mark as read if from other user
      if (newMessage.sender != senderId) {
        newMessage.status = 'read';
        _markMessagesAsRead();
      }

      // Save to local storage
      _saveMessageLocally(newMessage);
    } catch (e) {
      print('Error handling incoming message: $e');
    }
  }

  // Add this function to show message status indicators
  Widget _buildMessageStatus(ChatModel message) {
    // Only show status for sent messages (not received)
    if (message.sender != senderId) {
      return const SizedBox.shrink();
    }

    IconData statusIcon;
    Color statusColor;
    String? statusText;

    switch (message.status) {
      case 'pending':
        statusIcon = Icons.access_time;
        statusColor = Colors.grey;
        statusText = null; // Just show the icon for pending
        break;
      case 'sent':
        statusIcon = Icons.check;
        statusColor = Colors.grey;
        statusText = null; // Just show the icon for sent
        break;
      case 'delivered':
        statusIcon = Icons.done_all;
        statusColor = Colors.grey;
        statusText = null; // Just show the icon for delivered
        break;
      case 'read':
        statusIcon = Icons.done_all;
        statusColor = Colors.blue;
        statusText = null; // Just show the icon for read
        break;
      default:
        statusIcon = Icons.access_time;
        statusColor = Colors.grey;
        statusText = null; // Default to pending
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          statusIcon,
          size: 14,
          color: statusColor,
        ),
        if (statusText != null) ...[
          SizedBox(width: 2.w),
          Text(
            statusText,
            style: TextStyle(
              fontSize: 9.sp,
              color: statusColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ],
    );
  }

  // Add this function to mark messages as read
  void _markMessagesAsRead() {
    // Find messages that are not from the current user and not marked as read
    final unreadMessages = messages
        .where((msg) =>
            msg.sender != senderId && msg.status != 'read' && msg.id != null)
        .toList();

    if (unreadMessages.isEmpty) return;

    // Get the IDs of these messages
    final messageIds = unreadMessages.map((msg) => msg.id!).toList();

    // Update local status
    for (var msg in unreadMessages) {
      msg.status = 'read';
    }

    // Save updated status to local storage
    for (var msg in unreadMessages) {
      _chatLocalRepository.saveMessage(msg);
    }

    // Print debug info
    print('Marking ${messageIds.length} messages as read: $messageIds');
    _debugPrintMessageStatuses();

    // If we have a socket connection, send read receipts
    try {
      _chatBloc.add(ChatMarkMessagesAsReadEvent(messageIds));
      print('Marked ${messageIds.length} messages as read');
    } catch (e) {
      print('Error marking messages as read: $e');
    }
  }

  // Helper function to format message timestamp
  String _formatTime(DateTime? timestamp) {
    if (timestamp == null) return '';
    return '${timestamp.hour.toString().padLeft(2, '0')}:${timestamp.minute.toString().padLeft(2, '0')}';
  }

  // Helper function to format date for headers
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final yesterday = DateTime.now().subtract(Duration(days: 1));

    if (_isSameDay(date, now)) {
      return 'Today';
    } else if (_isSameDay(date, yesterday)) {
      return 'Yesterday';
    } else {
      // For other dates, show formatted date
      return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
    }
  }

  // Helper function to check if two dates are the same day
  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  // Add function to load more messages using pagination
  void _loadMoreMessages() {
    if (_isLoadingMoreMessages || _isLoadingHistory || messages.isEmpty) {
      return; // Already loading or no messages to paginate from
    }

    print(
        'Loading older messages before ${messages.first.timestamp?.toIso8601String()}');

    // IMPORTANT: Mark that we're loading more messages to prevent cache clearing
    setState(() {
      _isLoadingMoreMessages = true;
    });

    // Find the oldest message timestamp to use as cursor
    final oldestMessageTime = messages.first.timestamp;
    if (oldestMessageTime == null) {
      print('Cannot load more messages: No timestamp on oldest message');
      setState(() {
        _isLoadingMoreMessages = false;
      });
      return;
    }

    // Create conversation ID based on whether oppositeId is empty
    String conversationId;
    if (oppositeId.isEmpty) {
      // For empty recipient messages, use the special format
      conversationId = '-$senderId';
      print(
          'Using empty recipient conversation ID for pagination: $conversationId');
    } else {
      // For normal messages with a recipient
      final List<String> users = [senderId!, oppositeId];
      users.sort(); // Sort to ensure consistent ID regardless of order
      conversationId = users.join('-');
    }

    // Create the payload for fetching older messages
    final Map<String, dynamic> payload = {
      'user1': senderId!,
      'user2': oppositeId.isEmpty ? senderId : oppositeId,
      'type1': widget.data['senderType'] ?? 'user',
      'type2': oppositeId.isEmpty
          ? (widget.data['senderType'] ?? 'user')
          : (widget.data['oppositeModel'] ?? 'user'),
      'limit': 20, // Fetch 20 messages at a time for pagination
      'beforeTimestamp': oldestMessageTime.toIso8601String(),
      'conversationId': conversationId,
    };

    // Add the isBroadcast flag for broadcast conversations
    if (oppositeId.isEmpty || widget.data['isBroadcast'] == true) {
      payload['isBroadcast'] = true;
    }

    // Send fetch request
    _chatBloc.add(ChatFetchMessagesEvent(payload));
  }

  bool _isMessageForCurrentChat(ChatModel msg) {
    // Debug output for all messages to help troubleshoot broadcast issues
    bool appearsToBeBroadcast = msg.isBroadcast == true ||
        (msg.conversationId != null && msg.conversationId!.startsWith('-'));

    if (appearsToBeBroadcast || oppositeId.isEmpty) {
      print('=== MESSAGE VALIDATION [Broadcast=${appearsToBeBroadcast}] ===');
      print(
          'Message: ${msg.message?.substring(0, math.min(20, msg.message?.length ?? 0))}...');
      print(
          'isBroadcast: ${msg.isBroadcast}, conversationId: ${msg.conversationId}');
      print('sender: ${msg.sender}, recipient: ${msg.recipient}');
      print('Current view is broadcast: ${oppositeId.isEmpty}');
    }

    // Handle null or empty IDs safely
    if (senderId == null || senderId!.isEmpty) {
      print('Cannot validate message: SenderId is invalid');
      return false;
    }

    // Special case: If this is a broadcast message
    final bool isBroadcast = oppositeId.isEmpty;
    if (isBroadcast) {
      // If we're in broadcast view, look for messages with the special conversationId format
      if (msg.conversationId != null && msg.conversationId == '-$senderId') {
        print('Message is valid: Matches broadcast conversation ID');
        return true;
      }

      // Also check for sent broadcast messages that might have different formats
      if (msg.sender == senderId) {
        if (msg.isBroadcast == true ||
            (msg.recipientModel == 'user' && msg.recipient == senderId)) {
          print('Message is valid: Is a broadcast message from current user');
          return true;
        }
      }

      // Messages TO this sender with broadcast indication
      if (msg.recipient == senderId && msg.isBroadcast == true) {
        print('Message is valid: Is a broadcast message to current user');
        return true;
      }

      return false;
    }

    // Normal 1:1 chat messages - standard validation
    // Make sure message has valid sender and recipient
    if (msg.sender == null ||
        msg.sender!.isEmpty ||
        msg.recipient == null ||
        msg.recipient!.isEmpty) {
      print('Cannot validate message: Message has invalid sender/recipient');
      return false;
    }

    // Preferred: Use conversationId if available
    if (msg.conversationId != null && msg.conversationId!.isNotEmpty) {
      // Create the expected conversation ID in the same way
      List<String> users = [senderId!, oppositeId];
      users.sort(); // Sort to ensure consistent ID
      String expectedConversationId = users.join('-');

      // Check if the conversation IDs match
      if (msg.conversationId == expectedConversationId) {
        return true;
      }
    }

    // Fallback: Check sender/recipient combinations
    return ((msg.sender == senderId && msg.recipient == oppositeId) ||
        (msg.sender == oppositeId && msg.recipient == senderId));
  }

  bool _isMessageAlreadyInList(ChatModel newMsg) {
    // Check ID first (fastest check)
    if (newMsg.id != null) {
      if (messages.any((msg) => msg.id == newMsg.id)) {
        print('Message with ID ${newMsg.id} already exists in the list');
        return true;
      }
    }

    // Check for temp ID match
    if (newMsg.tempId != null) {
      if (messages.any((msg) => msg.tempId == newMsg.tempId)) {
        print(
            'Message with tempID ${newMsg.tempId} already exists in the list');
        return true;
      }
    }

    // Check for content + sender + recipient + time closeness
    for (final msg in messages) {
      // For messages with empty recipients, only check sender
      bool senderRecipientMatch = false;

      if (oppositeId.isEmpty) {
        // For empty recipient chats, just check the sender matches and both have empty recipients
        senderRecipientMatch = (msg.sender == newMsg.sender &&
            (msg.recipient == null || msg.recipient!.isEmpty) &&
            (newMsg.recipient == null || newMsg.recipient!.isEmpty));
      } else {
        // For normal chats, check standard sender/recipient match
        senderRecipientMatch =
            (msg.sender == newMsg.sender && msg.recipient == newMsg.recipient);
      }

      // Skip if sender/recipient don't match
      if (!senderRecipientMatch) {
        continue;
      }

      // Skip if content doesn't match
      if (msg.message != newMsg.message) {
        continue;
      }

      // If we have timestamps, check if they're close (within 2 seconds)
      if (msg.timestamp != null && newMsg.timestamp != null) {
        final timeDifference =
            msg.timestamp!.difference(newMsg.timestamp!).inSeconds.abs();
        if (timeDifference < 2) {
          print(
              'Found duplicate message based on content and timestamp proximity');
          return true;
        }
      } else if (msg.message == newMsg.message) {
        // If no timestamp but content matches, assume duplicate
        print('Found duplicate message based on content (no timestamp)');
        return true;
      }
    }

    return false;
  }

  // Save a single message to local storage
  void _saveMessageLocally(ChatModel message) {
    if (_disposed) return;

    // Skip messages that don't belong to this conversation
    if (!_isMessageForCurrentChat(message)) {
      print(
          'Not saving message to local storage: not part of current conversation');
      return;
    }

    // Ensure message has a conversationId
    if (message.conversationId == null || message.conversationId!.isEmpty) {
      // For broadcast messages, use the special format
      if (message.isBroadcast == true || oppositeId.isEmpty) {
        message.conversationId = '-$senderId';
      } else {
        // For normal messages
        List<String> users = [senderId!, oppositeId];
        users.sort();
        message.conversationId = users.join('-');
      }
      print(
          'Added missing conversationId for local storage: ${message.conversationId}');
    }

    // For broadcast messages, ensure isBroadcast flag is set
    if (message.conversationId != null &&
        message.conversationId!.startsWith('-') &&
        message.isBroadcast != true) {
      message.isBroadcast = true;
    }

    // Validate timestamp - ensure it's not in the future
    if (message.timestamp != null) {
      final now = DateTime.now();
      if (message.timestamp!.isAfter(now.add(const Duration(seconds: 1)))) {
        print(
            'Warning: Message being saved has future timestamp (${message.timestamp!.toIso8601String()}), adjusting to current time');
        message.timestamp = now;
      }
    } else {
      // If no timestamp provided, set to current time
      message.timestamp = DateTime.now();
      print('No timestamp on message being saved, setting to current time');
    }

    // Don't log every message save to reduce console spam
    final contentPreview =
        message.message != null && message.message!.length > 20
            ? '${message.message!.substring(0, 20)}...'
            : message.message;
    print(
        'Saving message to local storage: $contentPreview (ConvId: ${message.conversationId})');

    // Save message to local repository
    _chatLocalRepository.saveMessage(message).then((success) {
      if (!success) {
        print(
            'Warning: Failed to save message to local storage: ${message.id}');
      }
    }).catchError((e) {
      print('Error saving message to local storage: $e');
    });
  }

  // Helper widgets for different states
  Widget _buildLoadingView(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(),
          const SizedBox(height: 16),
          Text(message, style: const TextStyle(fontSize: 16)),
        ],
      ),
    );
  }

  Widget _buildEmptyView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.chat_bubble_outline, size: 64, color: Colors.grey),
          const SizedBox(height: 16),
          const Text(
            'No messages yet',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          const Text(
            'Start the conversation!',
            style: TextStyle(color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorView(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          const Text(
            'Error loading messages',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: Text(
              error,
              textAlign: TextAlign.center,
              style: const TextStyle(color: Colors.grey),
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _retryConnection,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildConnectionFailedView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.wifi_off, size: 64, color: Colors.orange),
          const SizedBox(height: 16),
          const Text(
            'Connection Failed',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: Text(
              _errorMessage.isEmpty
                  ? 'Could not connect to chat server'
                  : _errorMessage,
              textAlign: TextAlign.center,
              style: const TextStyle(color: Colors.grey),
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _retryConnection,
            child: const Text('Retry Connection'),
          ),
          const SizedBox(height: 16),
          // Add an option to clear all cache for fresh start
          TextButton.icon(
            onPressed: () async {
              // Ask for confirmation first
              final bool confirm = await showDialog(
                    context: context,
                    builder: (context) => AlertDialog(
                      title: const Text('Clear Chat Data?'),
                      content: const Text(
                        'This will delete all locally stored messages. Use this option only if you\'re experiencing issues with the chat. Your messages on the server will not be affected.',
                      ),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(false),
                          child: const Text('Cancel'),
                        ),
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(true),
                          child: const Text('Clear Data'),
                        ),
                      ],
                    ),
                  ) ??
                  false;

              if (confirm && mounted && !_disposed) {
                _forceClearAllChatData();
              }
            },
            icon: const Icon(Icons.cleaning_services, color: Colors.red),
            label: const Text(
              'Clear Chat Data',
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }

  // Process messages
  void _processMessages(List<ChatModel> newMessages) {
    if (_disposed || newMessages.isEmpty) return;

    print('Processing ${newMessages.length} new messages from server');

    // Create a map of existing messages by ID for quick lookup
    final Map<String, ChatModel> existingMessagesById = {};
    final Map<String, ChatModel> existingMessagesByTempId = {};

    for (final msg in messages) {
      // Track by real ID
      if (msg.id != null) {
        existingMessagesById[msg.id!] = msg;
      }
      // Track by temp ID
      if (msg.tempId != null) {
        existingMessagesByTempId[msg.tempId!] = msg;
      }
    }

    // Track changes for state update
    final List<ChatModel> updatedMessages = List.from(messages);
    bool hasChanges = false;
    List<ChatModel> messagesToSave = [];

    for (final newMsg in newMessages) {
      // Skip messages that don't belong to this conversation
      if (!_isMessageForCurrentChat(newMsg)) {
        print('Skipping message not for current chat: ${newMsg.message}');
        continue;
      }

      // Validate timestamp - ensure it's not in the future
      if (newMsg.timestamp != null) {
        final now = DateTime.now();
        if (newMsg.timestamp!.isAfter(now.add(const Duration(seconds: 1)))) {
          print(
              'Warning: Message in batch has future timestamp (${newMsg.timestamp!.toIso8601String()}), adjusting to current time');
          newMsg.timestamp = now;
        }
      } else {
        // If no timestamp provided, set to current time
        newMsg.timestamp = DateTime.now();
      }

      // Case 1: Check if this message already exists by ID
      bool messageExists = false;
      if (newMsg.id != null && existingMessagesById.containsKey(newMsg.id)) {
        // Update existing message with same ID if status or other fields changed
        final existingMsg = existingMessagesById[newMsg.id!]!;
        if (existingMsg.status != newMsg.status ||
            existingMsg.timestamp != newMsg.timestamp) {
          final index = updatedMessages.indexWhere((m) => m.id == newMsg.id);
          if (index >= 0) {
            print('Updating existing message: ${newMsg.id}');
            updatedMessages[index] = newMsg;
            hasChanges = true;
            messagesToSave.add(newMsg); // Save updated message
          }
        }
        messageExists = true;
      }

      // Case 2: Check if there's a message with matching temp ID that should be updated with real ID
      else if (newMsg.id != null &&
          newMsg.tempId != null &&
          existingMessagesByTempId.containsKey(newMsg.tempId)) {
        final index =
            updatedMessages.indexWhere((m) => m.tempId == newMsg.tempId);
        if (index >= 0) {
          print(
              'Updating message with tempId ${newMsg.tempId} to use real ID ${newMsg.id}');
          updatedMessages[index] = newMsg;
          hasChanges = true;
          messagesToSave.add(newMsg); // Save message with real ID
        }
        messageExists = true;
      }

      // Case 3: It's a completely new message
      if (!messageExists) {
        print('Adding new message: ${newMsg.message}');
        updatedMessages.add(newMsg);
        hasChanges = true;
        messagesToSave.add(newMsg); // Save new message
      }
    }

    // Only update state if there are changes
    if (hasChanges) {
      print('Updating state with ${updatedMessages.length} messages');
      setState(() {
        // Sort messages by timestamp - null timestamps go first
        updatedMessages.sort((a, b) {
          if (a.timestamp == null && b.timestamp == null) return 0;
          if (a.timestamp == null) return -1;
          if (b.timestamp == null) return 1;
          return a.timestamp!.compareTo(b.timestamp!);
        });

        messages = updatedMessages;
      });

      // Save messages locally for persistence
      if (messagesToSave.isNotEmpty) {
        _saveMessagesLocally(messagesToSave);
      }

      // Mark new messages as read
      _markMessagesAsRead();

      // Scroll to bottom if there are new messages
      _scrollToBottom();
    } else {
      print('No changes detected, state not updated');
    }
  }

  // Save a batch of messages to local storage
  void _saveMessagesLocally(List<ChatModel> messagesToSave) {
    if (_disposed || messagesToSave.isEmpty) return;

    for (final message in messagesToSave) {
      // Skip messages that don't belong to this conversation
      if (!_isMessageForCurrentChat(message)) {
        continue;
      }

      // Validate timestamp - ensure it's not in the future
      if (message.timestamp != null) {
        final now = DateTime.now();
        if (message.timestamp!.isAfter(now.add(const Duration(seconds: 1)))) {
          print(
              'Warning: Message in batch save has future timestamp (${message.timestamp!.toIso8601String()}), adjusting to current time');
          message.timestamp = now;
        }
      } else {
        // If no timestamp provided, set to current time
        message.timestamp = DateTime.now();
      }

      // Ensure message has a conversationId
      if (message.conversationId == null || message.conversationId!.isEmpty) {
        // For broadcast messages, use the special format
        if (message.isBroadcast == true || oppositeId.isEmpty) {
          message.conversationId = '-$senderId';
        } else {
          // For normal messages
          List<String> users = [senderId!, oppositeId];
          users.sort();
          message.conversationId = users.join('-');
        }
      }

      // For broadcast messages, ensure isBroadcast flag is set
      if (message.conversationId != null &&
          message.conversationId!.startsWith('-') &&
          message.isBroadcast != true) {
        message.isBroadcast = true;
      }

      // Save each message to local repository
      _chatLocalRepository.saveMessage(message).then((success) {
        if (!success) {
          print(
              'Warning: Failed to save message to local storage: ${message.id}');
        }
      }).catchError((e) {
        print('Error saving message to local storage: $e');
      });
    }
  }

  // Update a temporary message with a real ID from the server
  void _updateMessageWithServerId(
      String tempId, String serverId, String status) {
    if (_disposed || !mounted) return;

    print(
        'Updating message with tempId $tempId to use server ID $serverId (status: $status)');

    bool found = false;
    // Find and update the message in our list
    for (int i = 0; i < messages.length; i++) {
      if (messages[i].tempId == tempId) {
        setState(() {
          messages[i].id = serverId;
          messages[i].status = status;
          found = true;
        });

        // Update the message in local storage too
        _saveMessageLocally(messages[i]);
        break;
      }
    }

    if (!found) {
      print('Could not find message with tempId $tempId to update');
    }
  }

  // Listen for message confirmations from the server
  void _listenForMessageConfirmations() {
    try {
      _chatBloc.stream.listen((state) {
        if (_disposed || !mounted) return;

        if (state is ChatMessageStatusUpdated) {
          // Find message by ID and update its status
          final index = messages.indexWhere((msg) => msg.id == state.messageId);
          if (index != -1) {
            setState(() {
              messages[index].status = state.status;
              // Update in local storage
              _saveMessageLocally(messages[index]);
            });
          }
        } else if (state is ChatMessageReceived) {
          // This is handled separately in the bloc listener
        } else if (state is ChatMessagesMarkedAsRead) {
          // Update status of multiple messages
          for (final messageId in state.messageIds) {
            final index = messages.indexWhere((msg) => msg.id == messageId);
            if (index != -1) {
              setState(() {
                messages[index].status = 'read';
                // Update in local storage
                _saveMessageLocally(messages[index]);
              });
            }
          }
        }
      });
    } catch (e) {
      print('Error setting up message confirmation listener: $e');
    }
  }

  // Add better message debugging
  void _debugPrintMessageStatuses() {
    print("===== CURRENT MESSAGE STATUSES =====");
    for (int i = 0; i < messages.length; i++) {
      final msg = messages[i];
      print(
          "Message ${i + 1}: '${msg.message?.substring(0, math.min(10, msg.message?.length ?? 0))}${(msg.message?.length ?? 0) > 10 ? '...' : ''}' - Status: ${msg.status ?? 'null'}, ID: ${msg.id}, TempID: ${msg.tempId}");
    }
    print("===================================");
  }

  // Add better message debugging with ID focus
  void _debugPrintMessageIDInfo() {
    print("\n===== DEBUG MESSAGE ID INFO =====");
    for (int i = 0; i < messages.length; i++) {
      final msg = messages[i];
      print(
          "Message ${i + 1}: TempID: ${msg.tempId?.substring(0, math.min(16, msg.tempId?.length ?? 0)) ?? 'null'} → ServerID: ${msg.id?.substring(0, math.min(16, msg.id?.length ?? 0)) ?? 'null'} | Status: ${msg.status} | Content: '${msg.message?.substring(0, math.min(20, msg.message?.length ?? 0))}${(msg.message?.length ?? 0) > 20 ? '...' : ''}'");
    }
    print("================================\n");
  }

  // Debug method to print broadcast message information
  void _debugPrintBroadcastInfo(ChatModel message, String context) {
    final bool isBroadcast = message.isBroadcast == true ||
        (message.conversationId != null &&
            message.conversationId!.startsWith('-'));

    print('=== BROADCAST DEBUG [$context] ===');
    print(
        'Message: ${message.message?.substring(0, math.min(20, message.message?.length ?? 0))}...');
    print(
        'tempId: ${message.tempId}, id: ${message.id}, status: ${message.status}');
    print('sender: ${message.sender}, recipient: ${message.recipient}');
    print(
        'senderModel: ${message.senderModel}, recipientModel: ${message.recipientModel}');
    print('conversationId: ${message.conversationId}');
    print('isBroadcast flag: ${message.isBroadcast}');
    print('Detected as broadcast: $isBroadcast');
    print('Current user id: $senderId, oppositeId: $oppositeId');
    print('Current view is broadcast view: ${oppositeId.isEmpty}');
    print('============================');
  }

  // Add a method to clear local message cache
  Future<void> _clearLocalMessageCache() async {
    if (_disposed) return;

    print('Clearing local message cache for conversation');
    try {
      if (senderId == null || senderId!.isEmpty) {
        print('Cannot clear cache: Invalid senderId');
        return;
      }

      // Create conversation ID
      String conversationId;
      if (oppositeId.isEmpty) {
        // For broadcast messages
        conversationId = '-$senderId';
      } else {
        // For direct messages
        List<String> users = [senderId!, oppositeId];
        users.sort();
        conversationId = users.join('-');
      }

      // Clear messages from local storage
      await _chatLocalRepository.clearConversationMessages(conversationId);
      print('Local message cache cleared for conversationId: $conversationId');

      // Clear messages from UI
      setState(() {
        messages.clear();
      });
    } catch (e) {
      print('Error clearing local message cache: $e');
    }
  }

  // Method to force clear all chat data (for debug and recovery purposes)
  Future<void> _forceClearAllChatData() async {
    if (_disposed) return;

    try {
      print('FORCE CLEARING ALL CHAT DATA');

      // Show loading indicator
      setState(() {
        _isConnecting = true;
      });

      // Clear message list in UI
      setState(() {
        messages.clear();
      });

      // Clear local repository
      await _chatLocalRepository.clearAllMessages();

      // Disconnect socket to ensure clean reconnection
      if (_chatBloc.state is ChatConnected) {
        _chatBloc.add(ChatDisconnectEvent());

        // Wait a moment for disconnection to complete
        await Future.delayed(const Duration(milliseconds: 500));
      }

      // Reconnect and fetch fresh data
      _chatBloc.add(ChatConnectEvent(senderId!));

      // Reset UI state
      setState(() {
        _isConnecting = false;
        _connectionFailed = false;
        _errorMessage = "";
      });

      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Chat data cleared successfully'),
          duration: const Duration(seconds: 3),
        ),
      );
    } catch (e) {
      print('Error in force clearing chat data: $e');

      // Reset UI state even on error
      setState(() {
        _isConnecting = false;
        _connectionFailed = true;
        _errorMessage = "Error clearing chat data: $e";
      });
    }
  }

  // Helper method to create a test broadcast message to verify functionality
  void _sendTestBroadcastMessage() {
    // Disable this functionality
    return;
  }

  // Generate a safe timestamp that won't be in the future
  String _getSafeTimestamp() {
    // Get current time
    final now = DateTime.now();

    // Check server time offset if available
    final DateTime safeTime = now;

    // Log for debugging
    print('Generated message timestamp: ${safeTime.toIso8601String()}');

    return safeTime.toIso8601String();
  }

  // Add a new method to process older messages (for pagination)
  void _processOlderMessages(List<ChatModel> olderMessages) {
    if (_disposed || olderMessages.isEmpty) return;

    print('Processing ${olderMessages.length} older messages from pagination');

    // Create a map of existing messages by ID for quick lookup
    final Map<String, ChatModel> existingMessagesById = {};

    for (final msg in messages) {
      if (msg.id != null) {
        existingMessagesById[msg.id!] = msg;
      }
    }

    // Filter out messages we already have
    final List<ChatModel> newMessages = olderMessages
        .where((msg) =>
            msg.id != null && !existingMessagesById.containsKey(msg.id))
        .toList();

    if (newMessages.isEmpty) {
      print(
          'No new messages in pagination result - all messages already loaded');
      return;
    }

    print('Adding ${newMessages.length} new messages from pagination');

    // Add the new messages and sort
    final List<ChatModel> updatedMessages = List.from(messages);
    updatedMessages.addAll(newMessages);

    // Sort messages by timestamp
    updatedMessages.sort((a, b) {
      if (a.timestamp == null && b.timestamp == null) return 0;
      if (a.timestamp == null) return -1;
      if (b.timestamp == null) return 1;
      return a.timestamp!.compareTo(b.timestamp!);
    });

    // Update the state
    messages = updatedMessages;

    // Save the messages locally
    _saveMessagesLocally(newMessages);
  }

  // Handle keyboard appearance and other metrics changes
  @override
  void didChangeMetrics() {
    super.didChangeMetrics();
    // When keyboard appears/disappears, scroll to bottom after a slight delay
    if (!_disposed && mounted && messages.isNotEmpty) {
      // Add small delay to allow UI to settle after keyboard appears
      Future.delayed(Duration(milliseconds: 300), () {
        if (!_disposed && mounted) {
          _scrollToBottom();
        }
      });
    }
  }
}
