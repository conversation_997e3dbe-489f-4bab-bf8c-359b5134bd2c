import 'package:class_z/core/imports.dart';

Widget coachSlotConfirmationCard({
  required BuildContext context,
  required Color firstColor,
  Color? firstTextColor,
  required String firstText,
  required String address,
  required String center,
  required String date,
  required String course,
  required String time,
  required String classTime,
  required String numberOfStudent,
  required String numberOfStudentMax,
  required String ageStart,
  required String agefinish,
  required String coach,
  required String charges,
  bool sen = false,
}) {
  return Padding(
    padding: const EdgeInsets.symmetric(horizontal: 25),
    child: Container(
        decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20.r),
            boxShadow: [shadow(blurRadius: 15, opacity: 0.1)]),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                  color: firstColor,
                  borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(20.r),
                      topRight: Radius.circular(20.r))),
              child: Padding(
                padding: const EdgeInsets.all(15),
                child: customtext(
                  context: context,
                  newYear: firstText,
                  font: 15.sp,
                  weight: FontWeight.w500,
                  color: firstTextColor ?? Colors.white,
                ),
              ),
            ),
            const SizedBox(height: 12),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      customtext(
                        context: context,
                        newYear: date,
                        font: 15.sp,
                        weight: FontWeight.w500,
                      ),
                      Padding(
                        padding: const EdgeInsets.only(right: 31),
                        child: Row(
                          children: [
                            customSvgPicture(
                                imagePath: ImagePath.locationSvg,
                                height: 16.67,
                                width: 11.67),
                            const SizedBox(
                              width: 5.33,
                            ),
                            customtext(
                              context: context,
                              newYear: "center",
                              font: 15.sp,
                              weight: FontWeight.w500,
                            ),
                          ],
                        ),
                      )
                    ],
                  ),
                  const SizedBox(
                    height: 12,
                  ),
                  customtext(
                    context: context,
                    newYear: course,
                    font: 20.sp,
                    weight: FontWeight.w600,
                  ),
                  const SizedBox(
                    height: 11,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      customtext(
                        context: context,
                        newYear: time,
                        font: 20.sp,
                        weight: FontWeight.w500,
                      ),
                      const SizedBox(
                        width: 12,
                      ),
                      customtext(
                        context: context,
                        newYear: classTime,
                        font: 12.sp,
                        weight: FontWeight.w500,
                      ),
                    ],
                  ),
                  const SizedBox(
                    height: 13,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          customSvgPicture(
                              imagePath: ImagePath.groupSvg,
                              height: 11,
                              width: 11),
                          const SizedBox(
                            width: 5,
                          ),
                          customtext(
                            context: context,
                            newYear: () {
                              final current =
                                  int.tryParse(numberOfStudent) ?? 0;
                              final max = int.tryParse(numberOfStudentMax) ?? 0;
                              if (current >= max && max > 0) {
                                return "$max (Full)";
                              }
                              return "$current/$max";
                            }(),
                            font: 15.sp,
                            weight: FontWeight.w500,
                          ),
                        ],
                      ),
                      const SizedBox(
                        width: 18,
                      ),
                      customtext(
                        context: context,
                        newYear: "Age $ageStart- $agefinish",
                        font: 15.sp,
                        weight: FontWeight.w500,
                      ),
                    ],
                  ),
                  const SizedBox(
                    height: 9,
                  ),
                  customtext(
                    context: context,
                    newYear: " by $coach",
                    font: 15.sp,
                    weight: FontWeight.w500,
                  ),
                  SizedBox(
                    height: 17.h,
                  ),
                  customDivider(padding: 0, right: 21),
                  const SizedBox(
                    height: 20,
                  ),
                  customtext(
                      context: context,
                      newYear: "Coaching address:",
                      font: 15.sp,
                      weight: FontWeight.w500),
                  const SizedBox(
                    height: 20,
                  ),
                  address == 'center'
                      ? Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            customtext(
                                context: context,
                                newYear: "On-site coaching: ",
                                font: 15.sp,
                                weight: FontWeight.w500,
                                color: AppPallete.secondaryColor),
                            SizedBox(
                              width: 15.w,
                            ),
                            customtext(
                                context: context,
                                newYear: center,
                                font: 15.sp,
                                weight: FontWeight.w500),
                          ],
                        )
                      : Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            customtext(
                                context: context,
                                newYear: "Off-site coaching: ",
                                font: 15.sp,
                                weight: FontWeight.w500,
                                color: AppPallete.secondaryColor),
                            SizedBox(
                              width: 15.w,
                            ),
                            Flexible(
                              child: customtext(
                                  context: context,
                                  newYear: address,
                                  font: 15.sp,
                                  weight: FontWeight.w500),
                            ),
                          ],
                        ),
                  const SizedBox(
                    height: 20,
                  ),
                  customDivider(padding: 0, right: 21),
                  const SizedBox(
                    height: 20,
                  ),
                  customtext(
                      context: context,
                      newYear: "Charges:",
                      font: 15.sp,
                      weight: FontWeight.w500),
                  const SizedBox(
                    height: 15,
                  ),
                  customtext(
                      context: context,
                      newYear: "Student per class: ${charges} HKD",
                      font: 13.sp,
                      weight: FontWeight.w500),
                  const SizedBox(
                    height: 15,
                  ),
                  customtext(
                    context: context,
                    newYear: sen == true
                        ? "Special note: SEN-friendly"
                        : "Special note: SEN- Not friendly",
                    font: 13.sp,
                    weight: FontWeight.w500,
                  ),
                  const SizedBox(
                    height: 20,
                  ),
                  customDivider(padding: 0, right: 21),
                  const SizedBox(
                    height: 20,
                  ),
                  customtext(
                      context: context,
                      newYear: "By proceeding, you acknowledged that",
                      font: 13.sp,
                      weight: FontWeight.w400),
                  customtext(
                      context: context,
                      newYear:
                          "1. You have responsibility to coach the class on time with quality service",
                      font: 13.sp,
                      weight: FontWeight.w400),
                  customtext(
                      context: context,
                      newYear:
                          "2. Missed class may be subject to a service charge",
                      font: 13.sp,
                      weight: FontWeight.w400),
                  const SizedBox(
                    height: 20,
                  ),
                ],
              ),
            ),
          ],
        )),
  );
}
