import 'package:class_z/core/imports.dart';

class Coach<PERSON>ain extends StatefulWidget {
  final CoachModel coach;
  // ignore: prefer_const_constructors_in_immutables
  CoachMain({required this.coach, super.key});

  @override
  State<CoachMain> createState() => _CoachMainState();
}

class _CoachMainState extends State<CoachMain> {
  late CoachModel? coachData;
  List<EventModel>? events;
  List<PendingModel>? pendings = [];
  List<CenterData>? branches;
  late CenterBloc bloc;
  bool _isLoadingPendings = false;
  bool _hasMorePendings = true;
  final int _pendingsLimit =
      5; // Limit the number of pendings to fetch initially

  @override
  void initState() {
    super.initState();
    coachData = widget.coach;
    bloc = context.read<CenterBloc>();

    // Initialize notifications if coach ID is available
    if (coachData?.id != null) {
      final notificationBloc = context.read<NotificationBloc>();
      notificationBloc.add(GetNotificationsEvent(userId: coachData!.id!));
    }

    events = context.read<CenterBloc>().coachCheckIn;
    pendings = context.read<CenterBloc>().coachPendingReviews;
    branches = context.read<OwnerBloc>().branches;

    print('🔍 COACH MAIN INIT: events length: ${events?.length ?? 'null'}');
    print('🔍 COACH MAIN INIT: pendings length: ${pendings?.length ?? 'null'}');
    print('🔍 COACH MAIN INIT: branches length: ${branches?.length ?? 'null'}');

    // Always fetch data to ensure fresh data when coming back to screen
    print(
        '🔍 COACH MAIN INIT: Always calling _fetchData() to ensure fresh data');
    _fetchData();
  }

  void _fetchData() {
    print('🚀 COACH MAIN: _fetchData() called');
    setState(() {
      _isLoadingPendings = true;
    });

    final coachId = coachData?.id;
    final ownerId = coachData?.center?.ownerId;
    String date = dateGenerator(
        date: DateTime.now(), monthName: false, format: 'yyyy-MM-dd');

    print('🚀 COACH MAIN: coachId: $coachId, ownerId: $ownerId, date: $date');

    if (coachId == null || ownerId == null) {
      print("🚫 COACH MAIN: Coach ID or Owner ID is null, skipping fetch.");
      print("🚫 COACH MAIN: coachId: $coachId, ownerId: $ownerId");
      setState(() {
        _isLoadingPendings = false;
      });
      return;
    }

    // Reset pendings list when fetching fresh data
    pendings = [];
    _hasMorePendings = true;

    print('🚀 COACH MAIN: Adding GetAllEventsEvent with today=true');
    Future.wait([
      Future(() => bloc.add(GetAllEventsEvent(
            filterType: 'coachId',
            filterValue: coachId,
            date: date,
            today: true,
          ))),
      Future(() => bloc.add(GetPendingReviewsByCoachIdEvent(coachId))),
      Future(() =>
          context.read<OwnerBloc>().add(GetBranchsEvent(ownerId: ownerId))),
    ]);
  }

  void _loadMorePendings() {
    if (!_hasMorePendings || _isLoadingPendings) return;

    setState(() {
      _isLoadingPendings = true;
    });

    final coachId = coachData?.id;
    if (coachId == null) return;

    // Since pagination is not implemented in the event, we just refetch all data
    // In a real implementation, you would modify the API and event to support pagination
    bloc.add(GetPendingReviewsByCoachIdEvent(coachId));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: RefreshIndicator(
        onRefresh: () async {
          _fetchData();
        },
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: MultiBlocListener(
            listeners: [
              BlocListener<CenterBloc, CenterState>(
                listener: (context, state) {
                  print(
                      'DEBUG COACH MAIN: Received state: ${state.runtimeType}');
                  if (state is CenterLoadingState) {
                    print('DEBUG COACH MAIN: Loading state');
                    // Don't show full screen loading for better UX
                    // CircularProgressIndicator();
                  } else if (state is CenterErrorState) {
                    print('DEBUG COACH MAIN: Error state: ${state.message}');
                    setState(() {
                      _isLoadingPendings = false;
                    });
                    errorState(context: context, error: state.message);
                  } else if (state is EventListFetchSuccessToday) {
                    print(
                        'DEBUG COACH MAIN: EventListFetchSuccessToday received with ${state.events.length} events');
                    state.events.forEach((event) {
                      print(
                          'DEBUG COACH MAIN: Event - ${event.title}, Date: ${event.date}, ClassId: ${event.classId?.id}');
                      print(
                          'DEBUG COACH MAIN: Event classId null check: ${event.classId == null}');
                      print(
                          'DEBUG COACH MAIN: Event classId student length: ${event.classId?.student?.length}');
                      print(
                          'DEBUG COACH MAIN: Event classProviding: ${event.classId?.classProviding}');
                    });
                    setState(() {
                      events = state.events;
                    });
                    print(
                        'DEBUG COACH MAIN: events variable updated, length: ${events?.length}');
                  } else if (state is EventListFetchSuccess) {
                    print(
                        'DEBUG COACH MAIN: EventListFetchSuccess received with ${state.events.length} events (should not happen with today=true)');
                  } else if (state is PendingReviewFetchSuccess) {
                    setState(() {
                      // If we got fewer items than requested, there are no more to load
                      _hasMorePendings =
                          state.pendingReviews.length >= _pendingsLimit;

                      // Replace the list instead of appending since we're not implementing
                      // true pagination yet (would require API changes)
                      pendings = state.pendingReviews;
                      _isLoadingPendings = false;
                    });
                  }
                },
              ),
              BlocListener<OwnerBloc, OwnerState>(
                listener: (context, state) {
                  if (state is OwnerLoadingState)
                    CircularProgressIndicator();
                  else if (state is OwnerErrorState)
                    errorState(context: context, error: state.message);
                  else if (state is GetBranchSuccessState) {
                    setState(() {
                      branches = state.branches;
                    });
                  }
                },
              )
            ],
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _startProfile(rating: (coachData?.rating ?? 0.0).toString()),
                Padding(
                  padding: const EdgeInsets.only(top: 28, left: 24, right: 24),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      customtext(
                          context: context,
                          newYear: "Today's check-ins ",
                          font: 20.sp,
                          weight: FontWeight.w600),
                      GestureDetector(
                        onTap: () {
                          print(
                              '🎯 COACH MAIN: Navigating to checkin with ${events?.length ?? 0} events');
                          NavigatorService.pushNamed(AppRoutes.checkin,
                              arguments: events ?? []);
                        },
                        child: customtext(
                            context: context,
                            newYear: "see all",
                            font: 15.sp,
                            weight: FontWeight.w400),
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(left: 15.w, top: 11.h),
                  child: SizedBox(
                    height: 134,
                    child: (events == null || events!.isEmpty)
                        ? Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                customtext(
                                  context: context,
                                  newYear: "No Check-IN today",
                                  font: 20.sp,
                                ),
                                // if (events != null)
                                //   Text(
                                //       'Events list is empty but not null (length: ${events!.length})',
                                //       style: TextStyle(
                                //           fontSize: 12, color: Colors.grey)),
                                // if (events == null)
                                //   Text('Events list is null',
                                //       style: TextStyle(
                                //           fontSize: 12, color: Colors.grey)),
                              ],
                            ),
                          )
                        : ListView.builder(
                            scrollDirection: Axis.horizontal,
                            itemCount: events?.length,
                            itemBuilder: (context, index) {
                              final event = events![index];
                              print(
                                  '🏗️ BUILDER: Building CheckInCard for index $index, event ID: ${event.id}');
                              return Padding(
                                padding: const EdgeInsets.only(right: 10.0),
                                child: CheckInCard(
                                  context: context,
                                  event: event,
                                  onTap: () {
                                    NavigatorService.pushNamed(
                                      AppRoutes.verificationCode,
                                      arguments: {
                                        "classId": event.classId?.id,
                                        "totalStudent":
                                            event.classId?.student?.length,
                                        "bloc": context.read<CenterBloc>(),
                                      },
                                    );
                                  },
                                ),
                              );
                            },
                          ),
                  ),
                ),
                customDivider(width: 398.w, padding: 16.w),
                SizedBox(
                  height: 15.h,
                ),
                Padding(
                  padding: EdgeInsets.only(left: 37.w, right: 36.w),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      _fourRow(
                          context: context,
                          title: "Upcoming",
                          number: events
                                  ?.where((event) =>
                                      event.date != null &&
                                      event.date!.isAfter(DateTime.now()))
                                  .length ??
                              0,
                          width: 60.w),
                      _fourRow(
                          context: context,
                          title: "Required",
                          number: pendings?.length ?? 0,
                          width: 47.w),
                      _fourRow(
                          context: context,
                          title: "Vacancy",
                          number: (() {
                            int vacancyCount = 0;
                            if (events != null) {
                              for (var event in events!) {
                                final numberOfStudent =
                                    event.scheduleNumberOfStudent ??
                                        event.classId?.numberOfStudent ??
                                        0;
                                final studentCount =
                                    event.dateId?.students?.length ?? 0;
                                vacancyCount +=
                                    (numberOfStudent - studentCount) as int;
                              }
                            }
                            return vacancyCount;
                          })(),
                          width: 71.w),
                    ],
                  ),
                ),
                SizedBox(
                  height: 15.h,
                ),
                customDivider(width: 398.w, padding: 16.w),
                Padding(
                  padding: EdgeInsets.only(left: 24.w, right: 24.w, top: 20.h),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      customtext(
                          context: context,
                          newYear: "Pending review",
                          font: 20.sp,
                          weight: FontWeight.w600),
                      GestureDetector(
                        onTap: () {
                          NavigatorService.pushNamed(
                              AppRoutes.pendingReviewsbyClass,
                              arguments: {
                                'coachId':
                                    locator<SharedRepository>().getCoachId()
                              });
                        },
                        child: customtext(
                            context: context,
                            newYear: "see all",
                            font: 15.sp,
                            weight: FontWeight.w400),
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(left: 15.w, top: 11.h),
                  child: Column(
                    children: [
                      SizedBox(
                        height: 126.h,
                        child: _isLoadingPendings &&
                                (pendings == null || pendings!.isEmpty)
                            ? Center(
                                child: CircularProgressIndicator(),
                              )
                            : (pendings == null || pendings!.isEmpty)
                                ? Center(
                                    child: customtext(
                                      context: context,
                                      newYear: "No Pending Review",
                                      font: 20.sp,
                                    ),
                                  )
                                : ListView.separated(
                                    scrollDirection: Axis.horizontal,
                                    itemCount: pendings!.length,
                                    separatorBuilder: (_, __) =>
                                        SizedBox(width: 10.w),
                                    itemBuilder: (context, index) {
                                      final pending = pendings![index];
                                      return pendingReviewCard(
                                        context: context,
                                        imagePath:
                                            pending.studentId?.mainImage?.url ??
                                                "",
                                        name: pending.studentId?.fullname ??
                                            "Unknown name",
                                        course: pending.plainClassDetails
                                                ?.classProviding ??
                                            "",
                                        dateTime: dateGenerator(
                                            date: pending.event?.date),
                                        onTap: () {
                                          print(pending);
                                          NavigatorService.pushNamed(
                                            AppRoutes.centerProgressCheck,
                                            arguments: pending,
                                          ).then((_) {
                                            // Refresh the list when coming back from review
                                            _fetchData();
                                          });
                                        },
                                      );
                                    },
                                  ),
                      ),
                      if (_isLoadingPendings &&
                          pendings != null &&
                          pendings!.isNotEmpty)
                        Padding(
                          padding: EdgeInsets.only(top: 10.h),
                          child: SizedBox(
                            height: 20.h,
                            width: 20.w,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                            ),
                          ),
                        ),
                      if (_hasMorePendings &&
                          !_isLoadingPendings &&
                          pendings != null &&
                          pendings!.isNotEmpty)
                        Padding(
                          padding: EdgeInsets.only(top: 10.h),
                          child: TextButton(
                            onPressed: _loadMorePendings,
                            child: Text("Load More"),
                          ),
                        ),
                    ],
                  ),
                ),
                SizedBox(
                  height: 20.h,
                ),
                SizedBox(
                  height: 20.h,
                ),
                Padding(
                  padding: EdgeInsets.only(left: 24.w, right: 39.w),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      customtext(
                          context: context,
                          newYear: "Center branches",
                          font: 20.sp,
                          weight: FontWeight.w600),
                      // GestureDetector(
                      //   onTap: () {
                      //     NavigatorService.pushNamed(AppRoutes.pendingReview);
                      //   },
                      //   child: customtext(
                      //       context: context,
                      //       newYear: "edit",
                      //       font: 15.sp,
                      //       weight: FontWeight.w500),
                      // ),
                    ],
                  ),
                ),
                SizedBox(
                  height: 14.h,
                ),
                Padding(
                  padding: EdgeInsets.only(left: 9.w),
                  child: SizedBox(
                    height: 92.h,
                    child: (branches == null || branches!.isEmpty)
                        ? Center(
                            child: customtext(
                              context: context,
                              newYear: "No Branches available",
                              font: 20.sp,
                            ),
                          )
                        : ListView.separated(
                            scrollDirection: Axis.horizontal,
                            itemBuilder: (context, index) {
                              final branch = branches![index];
                              return centerBranchesCard(
                                  context: context,
                                  imagePath: imageStringGenerator(
                                      imagePath: branch.mainImage?.url ?? ''),
                                  center: branch.displayName ?? '',
                                  location: addressGenerator(
                                    address: branch.address,
                                    condition: 'city',
                                  ),
                                  rating: (branch.rating ?? 0).toString(),
                                  onTap: () {
                                    NavigatorService.pushNamed(
                                        AppRoutes.centreView,
                                        arguments: {
                                          'center': branch,
                                          'bottomView': false
                                        });
                                  });
                            },
                            separatorBuilder: (context, index) {
                              return SizedBox(width: 10.w);
                            },
                            itemCount: branches!.length,
                          ),
                  ),
                ),
                SizedBox(
                  height: 10.h,
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _startProfile({required String rating}) {
    return BlocBuilder<NotificationBloc, NotificationState>(
      builder: (context, state) {
        int unreadCount = 0;

        if (state is NotificationLoaded) {
          unreadCount =
              state.notifications.where((n) => n.isRead != true).length;
        } else if (state is NotificationError) {
          // Handle error state if needed
          debugPrint('Error loading notifications: ${state.message}');
        }

        return SizedBox(
          height: 300,
          child: Stack(children: [
            Container(
              height: 225,
              decoration: BoxDecoration(
                gradient: GradientProvider.getLinearGradient(),
              ),
            ),
            customTopBarOnlyIcon(
              context: context,
              icon2: Icons.settings,
              badgeCount1: unreadCount,
              badgeCount2: 0,
              onTap1: () {
                NavigatorService.pushNamed(
                  AppRoutes.notification,
                  arguments: coachData?.id,
                );
              },
              onTap2: () {
                NavigatorService.pushNamed(
                  AppRoutes.coachSettings,
                );
              },
            ),
            Column(
              children: [
                Padding(
                  padding: EdgeInsets.only(top: 87.0, left: 28, right: 34.w),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          CustomImageBuilder(
                            borderRadius: 91,
                            height: 91,
                            width: 91,
                            imagePath: imageStringGenerator(
                                imagePath: coachData?.mainImage?.url ?? ''),
                          ),
                          const SizedBox(width: 7),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              customtext(
                                context: context,
                                newYear: coachData?.displayName ?? 'Unknown',
                                font: 25.sp,
                                weight: FontWeight.w600,
                              ),
                              customtext(
                                context: context,
                                newYear: addressGenerator(
                                  address: coachData?.address,
                                  condition: 'both',
                                ),
                                font: 12,
                                weight: FontWeight.w600,
                              ),
                              customtext(
                                context: context,
                                newYear: coachData?.classZId ?? 'invalid id',
                                font: 12,
                                weight: FontWeight.w600,
                              ),
                            ],
                          ),
                        ],
                      ),
                      customRating(
                        context: context,
                        rating: rating,
                        fontSize: 22,
                        weight: FontWeight.w700,
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 12),
              ],
            ),
            Positioned(
              top: 189,
              bottom: 0,
              left: 0,
              right: 0,
              child: _stack(),
            ),
          ]),
        );
      },
    );
  }

  Widget _stack() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24.0),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 25),
        decoration: BoxDecoration(
            color: AppPallete.white,
            borderRadius: BorderRadius.circular(10),
            boxShadow: [shadow()]),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            messageIcon(
                context: context,
                svg: ImagePath.classSvg,
                title: 'Programs',
                height: 30,
                width: 30,
                onTap: () {
                  NavigatorService.pushNamed(AppRoutes.coachPrograms,
                      arguments: {
                        'centerId': coachData?.center?.id,
                      });
                }),
            messageIcon(
                context: context,
                svg: ImagePath.slotSvg,
                title: 'Slot',
                height: 30,
                width: 30,
                onTap: () {
                  NavigatorService.pushNamed(AppRoutes.coachSlot);
                }),
            messageIcon(
                context: context,
                svg: ImagePath.promoteSvg,
                title: 'Announce',
                height: 30,
                width: 30,
                onTap: () {
                  String coachId = locator<SharedRepository>().getCoachId();
                  NavigatorService.pushNamed(AppRoutes.centerAnnouncement,
                      arguments: {
                        'filterType': 'coachId',
                        'filterValue': coachId,
                      });
                }),
          ],
        ),
      ),
    );
  }
  // Widget _stack({
  //   required BuildContext context,
  //   required String rating,
  // }) {
  //   return Stack(
  //     children: [
  //       Container(
  //         height: 172.h,
  //         decoration: BoxDecoration(
  //           gradient: GradientProvider.getLinearGradient(),
  //         ),
  //       ),
  //       Positioned(top: 47, left: 19, child: customBackButton()),
  //       customTopBarOnlyIcon(
  //           context: context,
  //           icon1: Icons.notifications,
  //           icon2: Icons.settings,
  //           badgeCount1: 92,
  //           badgeCount2: 0,
  //           onTap1: () {
  //             NavigatorService.pushNamed(
  //               AppRoutes.notification,
  //               arguments: 'coach',
  //             );
  //           },
  //           onTap2: () {
  //             NavigatorService.pushNamed(
  //               AppRoutes.coachSettings,
  //             );
  //           }),
  //       Positioned(
  //           top: 89.h,
  //           left: 23.w,
  //           child: CustomImageBuilder(
  //               imagePath: imageStringGenerator(
  //                   imagePath: coachData?.mainImage?.url ?? ''),
  //               height: 91.h,
  //               width: 91.w,
  //               borderRadius: 99.r)),
  //       Positioned(
  //           top: 108.h,
  //           left: 124.w,
  //           child: Column(
  //             crossAxisAlignment: CrossAxisAlignment.start,
  //             children: [
  //               customtext(
  //                   context: context,
  //                   newYear: coachData!.displayName!,
  //                   color: Colors.white,
  //                   font: 25.sp,
  //                   weight: FontWeight.w600),
  //               SizedBox(
  //                 height: 3.h,
  //               ),
  //               customtext(
  //                   context: context,
  //                   newYear: "#1212",
  //                   color: Colors.white,
  //                   font: 12.sp,
  //                   weight: FontWeight.w600),
  //             ],
  //           )),
  //       Positioned(
  //         bottom: 11.h,
  //         right: 44.8.w,
  //         child: Container(
  //           decoration: BoxDecoration(
  //               boxShadow: [shadow(blurRadius: 15, opacity: 0.1)]),
  //           height: 40.h,
  //           child: Row(
  //             mainAxisAlignment: MainAxisAlignment.start,
  //             children: [
  //               customSvgPicture(
  //                   imagePath: ImagePath.starSvg,
  //                   color: AppPallete.rating,
  //                   height: 17.5.h,
  //                   width: 18.9.w),
  //               SizedBox(
  //                 width: 3.5.w,
  //               ),
  //               customtext(
  //                   context: context,
  //                   newYear: rating,
  //                   font: 20.sp,
  //                   weight: FontWeight.w700,
  //                   color: Colors.white)
  //             ],
  //           ),
  //         ),
  //       ),
  //     ],
  //   );
  // }

  Widget _fourRow(
      {required BuildContext context,
      required String title,
      required int number,
      required double width}) {
    return SizedBox(
      width: width,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Center(
            child: customtext(
                context: context,
                newYear: "$number",
                font: 20.sp,
                weight: FontWeight.w600,
                color: Colors.black),
          ),
          Center(
            child: customtext(
                context: context,
                newYear: title,
                font: 10.sp,
                // weight: FontWeight.w600,
                color: Colors.black),
          ),
        ],
      ),
    );
  }
}
