import 'package:class_z/core/imports.dart';

class CoachSlot extends StatefulWidget {
  const CoachSlot({super.key});

  @override
  State<CoachSlot> createState() => _CoachSlot();
}

class _CoachSlot extends State<CoachSlot> {
  DateTime _focusedDay = DateTime.now();
  DateTime? _selectedDay;
  late ValueNotifier<List<EventModel>> _selectedEvents;
  List<EventModel> events = [];
  List<String> eventDates = [];
  String coachId = locator<SharedRepository>().getCoachId();
  @override
  void initState() {
    _selectedEvents = ValueNotifier<List<EventModel>>([]);

    // print('🔍 COACH SLOT: initState called');
    // print('🔍 COACH SLOT: coachId = $coachId');
    // print(
    //     '🔍 COACH SLOT: Calling GetEventDatesEvent with filterType: coachId, filterValue: $coachId');

    context
        .read<CenterBloc>()
        .add(GetEventDatesEvent(filterType: 'coachId', filterValue: coachId));
    // Trigger initial event fetching

    // TODO: implement initState
    super.initState();
  }

  void _onselectedDay(DateTime selectedDay, DateTime focusedDay) {
    setState(() {
      String date = dateGenerator(
          date: selectedDay,
          monthName: false,
          format: 'yyyy-MM-dd'); // Format the date
      print(date);
      context.read<CenterBloc>().add(GetAllEventsEvent(
          filterType: 'coachId',
          filterValue: coachId,
          date: date)); // Fetch events for the selected day
      // Ensure coachId is provided
      _selectedDay = selectedDay;
      _focusedDay = focusedDay;
      _selectedEvents.value =
          _getEventsForDay(selectedDay); // Update the event list
    });
  }

  List<EventModel> _getEventsForDay(DateTime dateTime) {
    return events
        .where((event) =>
            event.date?.year == dateTime.year &&
            event.date?.month == dateTime.month &&
            event.date?.day == dateTime.day)
        .toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBarDouble(
        title: "My Slot",
        title2: "Upcoming schedule",
        leading: customBackButton(),
      ),
      body: BlocListener<CenterBloc, CenterState>(
        listener: (context, state) {
          if (state is CenterLoadingState)
            loadingState(context: context);
          else
            hideLoadingDialog(context);
          if (state is CenterErrorState)
            errorState(context: context, error: state.message);
          if (state is EventListFetchSuccess) {
            setState(() {
              events = state.events;
              _selectedEvents.value =
                  _getEventsForDay(_selectedDay ?? _focusedDay);
            });
          }
          if (state is EventDatesFetchSuccess) {
            setState(() {
              eventDates = state.eventDates;
              print('🔍 COACH SLOT: EventDatesFetchSuccess received');
              print('🔍 COACH SLOT: eventDates length: ${eventDates.length}');
              print('🔍 COACH SLOT: eventDates content: $eventDates');
            });
          }
        },
        child: SingleChildScrollView(
            child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              height: 27.h,
            ),
            _buildMonthNavigator(),
            SizedBox(height: 18.h),
            Builder(
              builder: (context) {
                print(
                    '🔍 COACH SLOT: Building CustomCalendar with eventDates: $eventDates');
                return CustomCalendar(
                  focusedDay: _focusedDay,
                  selectedDay: _selectedDay,
                  onDaySelected: _onselectedDay,
                  eventDates: eventDates,
                  onPageChanged: (focusedDay) {
                    setState(() {
                      _focusedDay = focusedDay;
                    });
                    _fetchEventDatesForMonth(focusedDay);
                  },
                );
              },
            ),
            const SizedBox(
              height: 17,
            ),
            ValueListenableBuilder<List<EventModel>>(
                valueListenable: _selectedEvents,
                builder: (context, value, child) {
                  return _listView(context: context);
                }),
            const SizedBox(
              height: 8,
            ),
          ],
        )),
      ),
    );
  }

  Widget _buildMonthNavigator() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IconButton(
            icon: const Icon(Icons.chevron_left),
            onPressed: () {
              setState(() {
                _focusedDay =
                    DateTime(_focusedDay.year, _focusedDay.month - 1, 1);
              });
              _fetchEventDatesForMonth(_focusedDay);
            },
          ),
          Text(
            DateFormat.yMMMM().format(_focusedDay),
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
          IconButton(
            icon: const Icon(Icons.chevron_right),
            onPressed: () {
              setState(() {
                _focusedDay =
                    DateTime(_focusedDay.year, _focusedDay.month + 1, 1);
              });
              _fetchEventDatesForMonth(_focusedDay);
            },
          ),
        ],
      ),
    );
  }

  void _fetchEventDatesForMonth(DateTime focusedDay) {
    print('🔍 COACH SLOT: _fetchEventDatesForMonth called for $focusedDay');
    print(
        '🔍 COACH SLOT: Calling GetEventDatesEvent with filterType: coachId, filterValue: $coachId');

    context
        .read<CenterBloc>()
        .add(GetEventDatesEvent(filterType: 'coachId', filterValue: coachId));
  }

  Widget _listView({required BuildContext context}) {
    CoachModel? coach = locator<SharedRepository>().getCoachData();

    // Filter out events with null or invalid class data to prevent "null-null" display
    final validEvents = events.where((event) {
      return event.classId != null &&
          event.classId!.id != null &&
          event.classId!.classProviding != null &&
          event.classId!.classProviding!.isNotEmpty;
    }).toList();

    if (validEvents.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.only(top: 20),
          child: customtext(
            context: context,
            newYear: "No scheduled classes found",
            font: 15.sp,
            weight: FontWeight.w500,
          ),
        ),
      );
    }

    return ListView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemBuilder: (context, index) {
          bool private = false;
          bool edit = false;
          EventModel event = validEvents[index];

          // Safe null handling for age group
          String ageGroup = "TBD";
          if (event.classId?.ageFrom != null && event.classId?.ageTo != null) {
            ageGroup = "${event.classId!.ageFrom} - ${event.classId!.ageTo}";
          }

          // Safe null handling for language - use schedule-specific first, then fall back to class-level
          String language = "Unknown Language";
          if (event.scheduleLanguageOptions != null &&
              event.scheduleLanguageOptions!.isNotEmpty) {
            language = event.scheduleLanguageOptions!.first;
          } else if (event.classId?.language != null &&
              event.classId!.language!.isNotEmpty) {
            language = event.classId!.language!.first;
          }

          return centerTimeSlotEditCard(
            context: context,
            imagepath: imageStringGenerator(
                imagePath: event.classId?.mainImage?.url ?? ''),
            edit: edit,
            private: private,
            course: event.title ?? event.classId?.classProviding ?? "Unknown",
            level: event.classId?.level ?? "Unknown",
            name: coach?.displayName ?? "Unknown",
            duration: event.durationMinutes ?? "0",
            classTime: event.startTime ?? "TBD",
            location: "center",
            language: language,
            ageGroup: ageGroup,
            totalStudent: event.scheduleNumberOfStudent ??
                event.classId?.numberOfStudent ??
                0,
            student: event.dateId?.students?.length ?? 0,
            fee: event.scheduleCharge ?? event.classId?.charge ?? 0,
            onTap: () {
              NavigatorService.pushNamed(AppRoutes.coachPressSlot,
                  arguments: event);
            },
          );
        },
        itemCount: validEvents.length);
  }
}
