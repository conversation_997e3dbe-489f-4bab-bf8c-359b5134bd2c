import 'package:class_z/core/imports.dart';

class CenterPendingReviewClass extends StatefulWidget {
  final Map<String, dynamic> data;
  const CenterPendingReviewClass({required this.data, super.key});

  @override
  State<CenterPendingReviewClass> createState() =>
      _CenterPendingReviewClassState();
}

class _CenterPendingReviewClassState extends State<CenterPendingReviewClass> {
  @override
  void initState() {
    String? centerId = widget.data['centerId'];
    String? coachId = widget.data['coachId'];
    if (centerId != null) {
      context.read<CenterBloc>().add(GetAllClassesEvent(centerId: centerId));
    } else {
      context
          .read<CenterBloc>()
          .add(GetAllClassesByCoachIdEvent(coachId: coachId!));
    }
    // TODO: implement initState
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        body: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(top: 44, left: 19),
                child: customBackButton(),
              ),
              Padding(
                padding: const EdgeInsets.only(left: 9.0),
                child: customtext(
                    context: context,
                    newYear: 'Pending Review',
                    font: 30,
                    weight: FontWeight.w500),
              ),
              BlocConsumer<CenterBloc, CenterState>(
                listener: (context, state) {
                  if (state is CenterLoadingState)
                    loadingState(context: context);
                  else
                    hideLoadingDialog(context);
                  if (state is CenterErrorState) {
                    errorState(context: context, error: state.message);
                  }
                },
                builder: (context, state) {
                  print('current state: $state');
                  if (state is ClassListFetchSuccess) {
                    print(state.classes.length);
                    if (state.classes.length == 0) {
                      return SizedBox(
                        child: Center(
                          child: Text('No classes available'),
                        ),
                      );
                    }
                    return ListView.builder(
                      padding: EdgeInsets.only(top: 23),
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: state.classes.length,
                      itemBuilder: (context, index) {
                        final classModel = state.classes[index];

                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            savedClassCard(
                                context: context,
                                edit: false,
                                imagePath:
                                    "${AppText.device}${classModel.mainImage?.url}",
                                locationType: _getCenterLocation(classModel),
                                title: classModel.classProviding ?? "Untitled",
                                category:
                                    "(${classModel.level ?? 'Level TBD'})",
                                coach: _getCenterCoach(classModel),
                                ageGroup: _getCenterAgeGroup(classModel),
                                rate: _getCenterRate(classModel),
                                time: _getCenterTime(classModel),
                                showHkd: true, // Show HKD for center role
                                onDelete: () {},
                                onTap: () {
                                  // Get the Bloc instance BEFORE the async gap (navigation)
                                  final centerBloc = context.read<CenterBloc>();
                                  NavigatorService.pushNamed(
                                      AppRoutes.pendingReviewStudent,
                                      arguments: {
                                        'classId': classModel.id,
                                        'name': classModel.classProviding
                                      }).then((_) {
                                    // After returning, re-fetch the class list
                                    // to ensure it's up-to-date.
                                    String? centerId = widget.data['centerId'];
                                    String? coachId = widget.data['coachId'];
                                    if (mounted) {
                                      // Check if the widget is still in the tree
                                      if (centerId != null) {
                                        centerBloc.add(GetAllClassesEvent(
                                            centerId: centerId));
                                      } else if (coachId != null) {
                                        centerBloc.add(
                                            GetAllClassesByCoachIdEvent(
                                                coachId: coachId));
                                      }
                                    }
                                  });
                                }),
                            const SizedBox(
                              height: 16,
                            )
                          ],
                        );
                      },
                    );
                  }
                  return SizedBox(
                    child: Center(
                      child: Text('No classesA available'),
                    ),
                  );
                },
              )
            ],
          ),
        ),
      ),
    );
  }

  // Helper methods for center pending review
  String _getCenterLocation(ClassModel classModel) {
    if (classModel.address?.isNotEmpty == true) {
      return classModel.address!;
    }
    if (classModel.mode == null) return "Location TBD";
    return classModel.mode! ? "In-Center" : "Online";
  }

  String _getCenterCoach(ClassModel classModel) {
    if (classModel.coach?.displayName?.isNotEmpty == true) {
      return classModel.coach!.displayName!;
    }
    return "Coach TBD";
  }

  String _getCenterAgeGroup(ClassModel classModel) {
    final ageFrom = classModel.ageFrom;
    final ageTo = classModel.ageTo;

    if (ageFrom == null && ageTo == null) return "Age TBD";
    if (ageFrom == null) return "Up to $ageTo";
    if (ageTo == null) return "$ageFrom+";
    return "$ageFrom-$ageTo";
  }

  String _getCenterRate(ClassModel classModel) {
    // First check the main class charge
    final mainCharge = classModel.charge;
    if (mainCharge != null && mainCharge > 0) {
      return mainCharge.toString();
    }

    // If main charge is null/0, check the dates for charge information
    if (classModel.dates?.isNotEmpty == true) {
      final dateCharge = classModel.dates?.first.charge;
      if (dateCharge != null && dateCharge > 0) {
        return dateCharge.toString();
      }
    }

    // Fallback to "0" if no charge found
    return "0";
  }

  String _getCenterTime(ClassModel classModel) {
    if (classModel.dates != null && classModel.dates!.isNotEmpty) {
      final duration = classModel.dates![0].durationMinutes;
      if (duration != null && duration.toString().isNotEmpty) {
        if (int.tryParse(duration.toString()) != null) {
          return "${duration}min";
        }
        return duration.toString();
      }
    }
    return "45min";
  }
}
