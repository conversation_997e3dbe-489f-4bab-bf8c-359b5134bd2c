import 'package:class_z/core/imports.dart';
import 'package:class_z/core/utils/image_picker_migration_helper.dart';
import 'package:class_z/core/widgets/compressed_image_picker.dart';
import 'package:http/http.dart' as http;

class CenterSendAnnouncement extends StatefulWidget {
  final EventModel event;
  const CenterSendAnnouncement({required this.event, super.key});

  @override
  State<CenterSendAnnouncement> createState() => _CenterSendAnnouncementState();
}

class _CenterSendAnnouncementState extends State<CenterSendAnnouncement> {
  TextEditingController messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  List<MessageEntity>? messages = [];
  List<ChildModel>? students = [];
  int? numberOfStudents;
  File? _selectedImage;
  @override
  void initState() {
    print("DIPU");

    numberOfStudents = widget.event.dateId?.students.length;
    // context

    context.read<AnnouncementBloc>().add(GetAnnouncementEvent(
        id: widget.event.dateId?.id.toString() ?? '', type: 'slot'));
    // TODO: implement initState
    super.initState();
  }

  @override
  void dispose() {
    messageController.dispose();
    // TODO: implement dispose
    super.dispose();
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.jumpTo(_scrollController.position.maxScrollExtent);
      }
    });
  }

  void _sendMessage() async {
    String text = messageController.text.trim();
    if (text.isNotEmpty || _selectedImage != null) {
      // Show loading state
      setState(() {
        // You could add a loading state here if needed
      });

      try {
        // Prepare the payload
        Map<String, dynamic> payload = {
          "slotId": widget.event.dateId?.id.toString(),
          "message": text.isNotEmpty
              ? text
              : "📷 Image", // Default message for image-only
          "title": widget.event.classId?.classProviding ?? '',
          "senderName":
              locator<SharedRepository>().getCenterData()?.displayName ?? '',
        };

        String? imageUrl;

        // If there's an image, upload it first
        if (_selectedImage != null) {
          try {
            // Upload the image using the existing upload API
            final uploadResult = await _uploadImage(_selectedImage!);
            if (uploadResult != null && uploadResult['success'] == true) {
              imageUrl = uploadResult['url'];
              payload["messageImage"] = {
                "url": imageUrl,
                "contentType": "image/jpeg"
              };
            } else {
              throw Exception('Failed to upload image');
            }
          } catch (uploadError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Failed to upload image: $uploadError')),
            );
            return; // Don't send message if image upload fails
          }
        }

        final newMessage = MessageModel(
          message: text.isNotEmpty ? text : "📷 Image",
          messageImage: imageUrl != null
              ? BusinessCertificate(
                  url: imageUrl,
                  contentType: 'image/jpeg',
                )
              : null,
          createdAt: DateTime.now(),
          id: DateTime.now().toString(),
        );

        print(newMessage);

        setState(() {
          messages?.add(newMessage);
          _selectedImage = null; // Clear selected image
        });

        // Send the message to the backend
        context
            .read<AnnouncementBloc>()
            .add(PostAnnouncementEvent(payload: payload));

        // Clear input field
        messageController.clear();

        // Scroll to the latest message
        Future.delayed(Duration(milliseconds: 100), () {
          _scrollToBottom();
        });
      } catch (error) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to send message: $error')),
        );
      }
    }
  }

  Future<Map<String, dynamic>?> _uploadImage(File imageFile) async {
    try {
      // Get the auth token
      final token = locator<SharedRepository>().getToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      // Create multipart request
      var request = http.MultipartRequest(
        'POST',
        Uri.parse('${AppText.device}/api/upload/image'),
      );

      // Add headers
      request.headers['auth-token'] = token;

      // Add the image file
      request.files.add(
        await http.MultipartFile.fromPath(
          'image',
          imageFile.path,
          contentType: MediaType('image', 'jpeg'),
        ),
      );

      // Add image type
      request.fields['imageType'] = 'messageImage';

      // Send the request
      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        return responseData;
      } else {
        throw Exception('Upload failed with status: ${response.statusCode}');
      }
    } catch (error) {
      print('Error uploading image: $error');
      return null;
    }
  }

  Future<void> _pickImage() async {
    print('📸 Image picker button tapped!');

    // Check if widget is still mounted
    if (!mounted) {
      print('📸 Widget not mounted, cancelling image pick');
      return;
    }

    try {
      final File? image =
          await ImagePickerMigrationHelper.showImagePickerDialog(
        context: context,
        type: ImagePickerType.gallery,
      );

      // Check if widget is still mounted after async operation
      if (!mounted) {
        print('📸 Widget disposed during image pick');
        return;
      }

      if (image != null) {
        print('📸 Image selected: ${image.path}');
        setState(() {
          _selectedImage = image;
        });
      } else {
        print('📸 No image selected');
      }
    } catch (e) {
      print('📸 Error picking image: $e');

      // Check if widget is still mounted before showing snackbar
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error picking image: $e')),
        );
      }
    }
  }

  void _removeSelectedImage() {
    setState(() {
      _selectedImage = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomAppBar(
            title: 'Announcement',
            subtitle: 'Send your Announcement',
            leading: customBackButton(),
          ),
          const SizedBox(
            height: 18,
          ),
          _classDetails(context: context),
          const SizedBox(
            height: 14,
          ),
          customDivider(),
          SizedBox(
            height: 15,
          ),
          // _buildStudentImage(
          //     context: context,
          //     childs: students,
          //     numberOfStudent: numberOfStudents.toString()),
          // SizedBox(
          //   height: 15,
          // ),
          // _buildMessage(context: context),
          Expanded(child: _announcement(context: context)),
          //     Spacer(),
          buildMessageInput(),
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _classDetails({required BuildContext context}) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 14.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              customtext(
                context: context,
                newYear: dateGenerator(date: widget.event.date),
                font: 12,
                weight: FontWeight.w500,
              ),
              customtext(
                context: context,
                newYear:
                    widget.event.classId?.classProviding ?? "No Class Name",
                font: 15.sp,
                weight: FontWeight.w500,
              ),
            ],
          ),
          RichText(
              text: TextSpan(children: [
            customSpanText(
              text: '${widget.event.startTime ?? "--"} - '
                  '${widget.event.endTime ?? "--"} ',
              fontSize: 15,
            ),
            customSpanText(
                text: '${widget.event.durationMinutes ?? "--"}', fontSize: 12),
          ])),
        ],
      ),
    );
  }

  Widget _announcement({required BuildContext context}) {
    return BlocConsumer<AnnouncementBloc, AnnouncementState>(
        listener: (context, state) {
      print('state $state');
      if (state is AnnouncementLoading)
        loadingState(context: context);
      else
        hideLoadingDialog(context);
      if (state is AnnouncementLoadedState || state is AnnouncementPostedState)
        _scrollToBottom();
      if (state is AnnouncementError) {
        print('error ${state.message}');
        errorState(context: context, error: state.message);
      }
    }, builder: (context, state) {
      print('current state: $state');
      if (state is AnnouncementLoadedState) {
        if (state.announcements == null) {
          return const Center(
            child: Text('No announcement available'),
          );
        }
        messages = state.announcements?.messages;
        print('studnets :${state.announcements?.slotId?.students}');
        students = state.announcements?.slotId?.students as List<ChildModel>;

        numberOfStudents = state.announcements?.slotId?.numberOfStudent;
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildStudentImage(
                context: context,
                childs: students,
                numberOfStudent: numberOfStudents.toString()),
            SizedBox(
              height: 15,
            ),
            _buildMessage(context: context)
          ],
        );
      }
      if (state is AnnouncementPostedState) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildStudentImage(
                context: context,
                childs: students,
                numberOfStudent: numberOfStudents.toString()),
            SizedBox(
              height: 15,
            ),
            _buildMessage(context: context)
          ],
        );
      }
      return const Center(
        child: Text('No anouncement available'),
      );
    });
  }

  Widget _buildStudentImage(
      {required BuildContext context,
      required List<ChildModel>? childs,
      required String numberOfStudent}) {
    if (childs == null || childs.isEmpty) {
      return Center(child: Text('No students available.'));
    }
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 15.0),
      child: Container(
        height: 135,
        decoration: BoxDecoration(
            color: AppPallete.white,
            boxShadow: [shadow()],
            borderRadius: BorderRadius.circular(20)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(top: 13.0, left: 13),
              child: textWithSvg(
                  context: context,
                  title: '${childs.length}/ $numberOfStudent',
                  space: 5,
                  imagePath: ImagePath.groupSvg),
            ),
            Expanded(
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                // shrinkWrap: true,
                padding: EdgeInsets.only(left: 12),
                //  physics: const NeverScrollableScrollPhysics(),
                itemCount: childs.length,
                itemBuilder: (context, index) {
                  final child = childs[index];
                  return Row(
                    children: [
                      student(
                        context: context,
                        imagePath: child.mainImage?.url ?? '',
                        name: child.fullname ?? "",
                        verified: "",
                      ),
                      SizedBox(
                        width: 12,
                      )
                    ],
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMessage({required BuildContext context}) {
    if (messages == null || messages?.length == 0) {
      return const Center(child: Text('No messages yet'));
    }
    return Expanded(
      child: ListView.builder(
        controller: _scrollController,
        itemCount: messages?.length,
        // reverse: true,
        padding: EdgeInsets.only(right: 15, left: 90),
        itemBuilder: (context, index) {
          final message = messages?[index];
          return Container(
            margin: const EdgeInsets.symmetric(vertical: 5),
            padding: const EdgeInsets.symmetric(horizontal: 11, vertical: 5),
            decoration: BoxDecoration(
              color: Colors.blue,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                // Display image if available
                if (message?.messageImage != null &&
                    message!.messageImage!.url != null &&
                    message.messageImage!.url!.isNotEmpty)
                  Container(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: _buildMessageImage(message.messageImage!.url!),
                    ),
                  ),
                // Display text message
                if (message?.message != null && message!.message!.isNotEmpty)
                  customtext(
                      context: context,
                      newYear: message.message!,
                      font: 15,
                      weight: FontWeight.w400,
                      color: AppPallete.white,
                      textAlign: TextAlign.right),
                customtext(
                    context: context,
                    newYear:
                        "send on ${dateGenerator(date: message?.createdAt, format: 'dd/MM/yyyy')}",
                    font: 10.sp)
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildMessageImage(String imageUrl) {
    // Check if it's a network URL or local file path
    if (imageUrl.startsWith('http') || imageUrl.startsWith('/uploads/')) {
      // Network image
      String fullUrl =
          imageUrl.startsWith('http') ? imageUrl : '${AppText.device}$imageUrl';

      return Image.network(
        fullUrl,
        width: 200,
        height: 150,
        fit: BoxFit.cover,
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return Container(
            width: 200,
            height: 150,
            color: Colors.grey[200],
            child: Center(
              child: CircularProgressIndicator(
                value: loadingProgress.expectedTotalBytes != null
                    ? loadingProgress.cumulativeBytesLoaded /
                        loadingProgress.expectedTotalBytes!
                    : null,
              ),
            ),
          );
        },
        errorBuilder: (context, error, stackTrace) {
          return Container(
            width: 200,
            height: 150,
            color: Colors.grey[300],
            child: const Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.broken_image,
                  color: Colors.grey,
                  size: 50,
                ),
                Text(
                  'Failed to load image',
                  style: TextStyle(color: Colors.grey, fontSize: 12),
                ),
              ],
            ),
          );
        },
      );
    } else {
      // Local file
      return Image.file(
        File(imageUrl),
        width: 200,
        height: 150,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            width: 200,
            height: 150,
            color: Colors.grey[300],
            child: const Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.broken_image,
                  color: Colors.grey,
                  size: 50,
                ),
                Text(
                  'Image not found',
                  style: TextStyle(color: Colors.grey, fontSize: 12),
                ),
              ],
            ),
          );
        },
      );
    }
  }

  Widget buildMessageInput() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10.0),
      child: Column(
        children: [
          // Image preview section
          if (_selectedImage != null)
            Container(
              margin: const EdgeInsets.only(bottom: 10),
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Row(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.file(
                      _selectedImage!,
                      width: 60,
                      height: 60,
                      fit: BoxFit.cover,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Image selected',
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                            color: Colors.grey[700],
                          ),
                        ),
                        Text(
                          'Tap × to remove',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[500],
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close, color: Colors.red),
                    onPressed: _removeSelectedImage,
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                ],
              ),
            ),
          // Message input row
          Row(
            children: [
              // Image picker button with better visibility
              Container(
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(25),
                ),
                child: IconButton(
                  icon: const Icon(
                    Icons.camera_alt,
                    color: Colors.blue,
                    size: 24,
                  ),
                  onPressed: _pickImage,
                  tooltip: 'Add image',
                  padding: const EdgeInsets.all(8),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: AuthField(
                  controller: messageController,
                  border: 50,
                  hintText: 'Type a message...',
                ),
              ),
              const SizedBox(width: 8),
              Container(
                decoration: BoxDecoration(
                  color: Colors.blue,
                  borderRadius: BorderRadius.circular(25),
                ),
                child: IconButton(
                  icon: const Icon(
                    Icons.send,
                    color: Colors.white,
                  ),
                  onPressed: _sendMessage,
                  padding: const EdgeInsets.all(8),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
