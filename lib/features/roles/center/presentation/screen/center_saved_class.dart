import 'package:class_z/core/imports.dart';
import 'package:class_z/features/roles/center/presentation/screen/center_class_details.dart';

class CenterSavedClass extends StatefulWidget {
  final String? centerId;
  const CenterSavedClass({this.centerId, super.key});

  @override
  State<CenterSavedClass> createState() => _CenterSavedClassState();
}

class _CenterSavedClassState extends State<CenterSavedClass> {
  String titleText = "Edit";
  String leftText = "Add program";
  bool title = false;

  @override
  void initState() {
    context
        .read<CenterBloc>()
        .add(GetAllClassesEvent(centerId: widget.centerId ?? ''));
    super.initState();
  }

  void _fetchClasses() {
    print('🔄 Refreshing classes list for center: ${widget.centerId}');
    context
        .read<CenterBloc>()
        .add(GetAllClassesEvent(centerId: widget.centerId ?? ''));
  }

  List<ClassModel> classes = [];
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: CustomAppBarDouble(
          title: "Saved Program",
          title2: "Setup the program offering",
          leading: customBackButton(),
        ),
        body: BlocListener<CenterBloc, CenterState>(
          listener: (context, state) {
            if (state is CenterLoadingState) {
              loadingState(context: context);
            } else
              hideLoadingDialog(context);
            if (state is CenterErrorState) {
              errorState(context: context, error: state.message);
            }
            if (state is ClassListFetchSuccess) {
              setState(() {
                classes =
                    state.classes; // Ensure that state.classes is not null.
              });
            }
            if (state is ClassDeletionSuccess) {
              _fetchClasses();
            }
          },
          child: RefreshIndicator(
            onRefresh: () async {
              _fetchClasses();
            },
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding:
                        EdgeInsets.only(left: 25.w, right: 36.w, top: 35.h),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        GestureDetector(
                          onTap: () async {
                            // Navigate to add class screen and wait for result
                            final result = await NavigatorService.pushNamed(
                                AppRoutes.centerAddClass);

                            // If a new class was created, refresh the list
                            if (result == true) {
                              print(
                                  '✅ New class created successfully, refreshing list');
                              _fetchClasses();
                            } else {
                              print(
                                  'ℹ️ No new class created or operation cancelled');
                            }
                          },
                          child: customtext(
                              context: context,
                              newYear: leftText,
                              font: 17.sp,
                              color: AppPallete.change,
                              weight: FontWeight.w400),
                        ),
                        GestureDetector(
                          onTap: () {
                            setState(() {
                              titleText = titleText == "Edit" ? "Done" : "Edit";
                              if (titleText == "Done") {
                                title = true;
                                leftText = "";
                              }
                              if (titleText == "Edit") {
                                title = false;
                                leftText = "Add program";
                              }
                            });
                          },
                          child: customtext(
                            context: context,
                            newYear: titleText,
                            font: 17.sp,
                            color: AppPallete.change,
                            weight: FontWeight.w400,
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 47.h),
                  (classes.isEmpty)
                      ? SizedBox(
                          height: getHeight(context: context) / 2,
                          child: Center(
                              child: customtext(
                                  context: context,
                                  newYear: "No saved Classes",
                                  font: 20.sp)),
                        )
                      : _branch(context: context, title: title),
                  SizedBox(
                    height: 10.h,
                  )
                ],
              ),
            ),
          ),
        ));
  }

  Widget _branch({required BuildContext context, required bool title}) {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemBuilder: (context, index) {
        final classModel = classes[index];

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            savedClassCard(
                context: context,
                edit: title,
                imagePath: classModel.mainImage?.url ?? "",
                title: classModel.classProviding?.isNotEmpty == true
                    ? classModel.classProviding!
                    : "Untitled",
                category: classModel.level?.isNotEmpty == true
                    ? "${classModel.level}"
                    : "",
                coach: _getCoachName(classModel),
                ageGroup: "${classModel.ageFrom ?? 0}-${classModel.ageTo ?? 0}",
                rate: _formatRate(classModel),
                time: _getTimeSlotInfo(classModel),
                locationType: getLocationType(classModel.address) ?? '',
                showHkd: true, // Show HKD for center role
                onDelete: () {
                  context
                      .read<CenterBloc>()
                      .add(DeleteClassEvent(classId: classModel.id ?? ""));
                },
                onTap: () {
                  NavigatorService.pushNamed(AppRoutes.centerClassDetails,
                      arguments: classModel);
                }),
            const SizedBox(
              height: 16,
            )
          ],
        );
      },
      itemCount: classes.length,
    );
  }

  String _getCoachName(ClassModel classModel) {
    // Extract coach name from the class model
    if (classModel.coach != null && classModel.coach is CoachModel) {
      final coach = classModel.coach as CoachModel;
      return coach.displayName ?? "Coach";
    }
    return "Coach"; // Default if no coach assigned
  }

  String _formatRate(ClassModel classModel) {
    // First check class-level charge
    if (classModel.charge != null && classModel.charge != 0) {
      return "${classModel.charge}";
    }

    // If class-level charge is null or 0, check schedule-level charges
    if (classModel.dates != null && classModel.dates!.isNotEmpty) {
      for (var date in classModel.dates!) {
        if (date.charge != null && date.charge != 0) {
          return "${date.charge}";
        }
      }
    }

    // If no charge found anywhere, return "0"
    return "0";
  }

  String _getTimeSlotInfo(ClassModel classModel) {
    // Check if the class has date slots with time information
    if (classModel.dates != null && classModel.dates!.isNotEmpty) {
      final firstSlot = classModel.dates!.first;

      // If we have both start and end time, calculate duration
      if (firstSlot.startTime != null && firstSlot.endTime != null) {
        final duration =
            _calculateDuration(firstSlot.startTime!, firstSlot.endTime!);
        return "${duration}mins";
      }

      // If we have duration already stored, show it
      if (firstSlot.durationMinutes != null) {
        // Remove 'mins' if it's already there and add it back for consistency
        String duration = firstSlot.durationMinutes!.replaceAll('mins', '');
        return "${duration}mins";
      }

      // If we only have start time, can't calculate duration
      if (firstSlot.startTime != null) {
        return firstSlot.startTime!;
      }
    }

    return ""; // Return empty if no time info available
  }

  int _calculateDuration(String startTime, String endTime) {
    try {
      // Parse time strings like "6:00 PM" or "12:45 PM"
      final startDateTime = _parseTimeString(startTime);
      final endDateTime = _parseTimeString(endTime);

      if (startDateTime != null && endDateTime != null) {
        final difference = endDateTime.difference(startDateTime);
        return difference.inMinutes;
      }
    } catch (e) {
      print("Error calculating duration: $e");
    }
    return 0;
  }

  DateTime? _parseTimeString(String timeString) {
    try {
      // Handle formats like "6:00 PM", "12:45 PM", "14:30", etc.
      final now = DateTime.now();

      // Remove extra spaces and convert to uppercase
      final cleanTime = timeString.trim().toUpperCase();

      if (cleanTime.contains('PM') || cleanTime.contains('AM')) {
        // 12-hour format
        final timePart = cleanTime.replaceAll(RegExp(r'[AP]M'), '').trim();
        final parts = timePart.split(':');

        if (parts.length == 2) {
          int hour = int.parse(parts[0]);
          int minute = int.parse(parts[1]);

          // Convert to 24-hour format
          if (cleanTime.contains('PM') && hour != 12) {
            hour += 12;
          } else if (cleanTime.contains('AM') && hour == 12) {
            hour = 0;
          }

          return DateTime(now.year, now.month, now.day, hour, minute);
        }
      } else {
        // 24-hour format
        final parts = cleanTime.split(':');
        if (parts.length == 2) {
          int hour = int.parse(parts[0]);
          int minute = int.parse(parts[1]);
          return DateTime(now.year, now.month, now.day, hour, minute);
        }
      }
    } catch (e) {
      print("Error parsing time string '$timeString': $e");
    }
    return null;
  }
}
