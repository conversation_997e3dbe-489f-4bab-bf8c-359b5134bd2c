import 'package:class_z/core/imports.dart';
import 'package:class_z/core/network/endpoints.dart';
import 'package:class_z/features/transactions/data/models/daily_transaction_model.dart';

class CenterTransaction extends StatefulWidget {
  const CenterTransaction({super.key});

  @override
  State<CenterTransaction> createState() => _CenterTransactionState();
}

class _CenterTransactionState extends State<CenterTransaction> {
  DateTime _focusday = DateTime.now();
  DateTime? _selectedDay;
  late ValueNotifier<List<EventModel>> _selectedEvents;
  List<EventModel> _events = [];

  // State for monthly total
  double _monthlyTotal = 0.0;
  bool _isLoadingMonthlyTotal = false;
  String _monthlyTotalErrorMessage = '';

  // State for daily transactions
  List<DailyTransactionModel> _dailyTransactions = [];
  bool _isDailyLoading = false;
  String _dailyErrorMessage = '';

  // State for transaction dates (for calendar dots)
  List<String> _transactionDates = [];
  bool _isLoadingTransactionDates = false;

  // Cache for transaction dates
  final Map<String, List<String>> _transactionDatesCache = {};
  final Map<String, DateTime> _cacheTimestamps = {};
  static const Duration _cacheValidDuration =
      Duration(minutes: 3); // Cache for 3 minutes

  @override
  void initState() {
    _selectedEvents = ValueNotifier<List<EventModel>>([]);
    super.initState();
    _selectedDay = _focusday;
    _fetchMonthlyTotal(_focusday);
    _fetchDailyTransactions(_selectedDay!);
    _fetchTransactionDates(_focusday);
  }

  void _onSelectedDay(DateTime selectedDay, DateTime focusday) {
    setState(() {
      _selectedDay = selectedDay;
      _focusday = focusday;
      _fetchDailyTransactions(selectedDay);
    });
  }

  void _onPageChanged(DateTime focusedDay) {
    setState(() {
      _focusday = focusedDay;
    });
    _fetchMonthlyTotal(focusedDay);
    _fetchTransactionDates(focusedDay);
  }

  void _previousMonth() {
    final newFocusedDay = DateTime(_focusday.year, _focusday.month - 1, 1);
    setState(() {
      _focusday = newFocusedDay;
    });
    _fetchMonthlyTotal(newFocusedDay);
    _fetchTransactionDates(newFocusedDay);
  }

  void _nextMonth() {
    final newFocusedDay = DateTime(_focusday.year, _focusday.month + 1, 1);
    setState(() {
      _focusday = newFocusedDay;
    });
    _fetchMonthlyTotal(newFocusedDay);
    _fetchTransactionDates(newFocusedDay);
  }

  String formatDate(DateTime selectedDay) {
    print(selectedDay);
    String date = DateFormat('dd/MM/yyyy').format(selectedDay);
    return date;
  }

  // Helper method to check transaction status
  bool _isTransactionCompleted(String status) {
    // Check various status strings that might indicate completion
    final completedStatuses = [
      'Completed',
      'completed',
      'COMPLETED',
      'Success',
      'success',
      'SUCCESS',
      'Paid',
      'paid',
      'PAID',
      'true',
      'TRUE',
      'True'
    ];

    // Log the status for debugging
    print('Transaction status: $status');

    // Check if status is a boolean string or actual boolean
    if (status.toLowerCase() == 'true' ||
        status == 'true' ||
        status == 'paid') {
      return true;
    }

    return completedStatuses.contains(status);
  }

  // Fetch monthly transaction total from the API
  Future<void> _fetchMonthlyTotal(DateTime date) async {
    if (!mounted) return;

    setState(() {
      _isLoadingMonthlyTotal = true;
      _monthlyTotalErrorMessage = '';
    });

    try {
      // Get user ID and auth token
      final sharedRepo = locator<SharedRepository>();
      final token = sharedRepo.getToken();

      final centerId = sharedRepo.getCenterId();

      if (centerId.isEmpty || token == null) {
        throw Exception('User not logged in or Center ID not available');
      }

      final apiService = ApiService(baseUrl: Endpoints.baseUrl);

      // Make the API call with the authentication token and updated route
      final response = await apiService.get(
        '${Endpoints.transactions}/center/$centerId/monthly-total', // Updated route
        token: token,
        queryParameters: {
          'year': date.year.toString(),
          'month': date.month.toString(),
        },
      );

      print('Monthly total response: $response');

      // ApiService.get already processes the response, so we can use it directly
      if (response is Map) {
        if (response['success'] == true) {
          if (mounted) {
            setState(() {
              _monthlyTotal = (response['data']?['total'] ?? 0.0).toDouble();
            });
          }
        } else {
          throw Exception(
              response['message'] ?? 'Failed to load monthly total');
        }
      } else {
        throw Exception('Invalid response format');
      }
    } catch (e) {
      setState(() {
        _monthlyTotalErrorMessage = 'Error: ${e.toString()}';
      });
      print('Error fetching monthly total: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingMonthlyTotal = false;
        });
      }
    }
  }

  // Fetch daily transactions for the selected date
  Future<void> _fetchDailyTransactions(DateTime date) async {
    if (!mounted) return;

    setState(() {
      _isDailyLoading = true;
      _dailyErrorMessage = '';
      _dailyTransactions = []; // Clear previous transactions
    });

    try {
      final sharedRepo = locator<SharedRepository>();
      final token = sharedRepo.getToken();
      final centerId = sharedRepo.getCenterId();

      if (centerId.isEmpty || token == null) {
        throw Exception(
            'User not logged in or Center ID not available for daily fetch');
      }

      final formattedDate = DateFormat('yyyy-MM-dd').format(date);
      final apiService = ApiService(baseUrl: Endpoints.baseUrl);

      print(
          'Fetching transactions for date: $formattedDate, centerId: $centerId');

      final response = await apiService.get(
        '${Endpoints.transactions}/center/$centerId/daily',
        token: token,
        queryParameters: {'date': formattedDate},
      );

      print('Daily transactions response: $response');

      if (response is Map && response['success'] == true) {
        if (response['data'] is List) {
          final List<dynamic> transactionsData = response['data'];
          print('Received ${transactionsData.length} transactions from API');

          if (mounted) {
            setState(() {
              _dailyTransactions = transactionsData.map((data) {
                print(
                    'Processing transaction: ${data['_id']} with status: ${data['paymentStatus'] ?? data['status']}');
                print('Full transaction data: $data');
                return DailyTransactionModel.fromJson(
                    data as Map<String, dynamic>);
              }).toList();

              print('Parsed ${_dailyTransactions.length} transactions');

              // Sort transactions by date (newest first)
              _dailyTransactions
                  .sort((a, b) => b.createdAt.compareTo(a.createdAt));
            });
          }
        } else if (response['data'] == null ||
            (response['data'] is List && (response['data'] as List).isEmpty)) {
          print('No transactions found for this date');
          if (mounted) {
            setState(() {
              _dailyTransactions = [];
            });
          }
        } else {
          throw Exception('Daily transactions data is not a list');
        }
      } else if (response is Map && response['success'] == false) {
        throw Exception(
            response['message'] ?? 'Failed to load daily transactions');
      } else {
        throw Exception('Invalid response format for daily transactions');
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _dailyErrorMessage = 'Error: ${e.toString()}';
        });
      }
      print('Error fetching daily transactions: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isDailyLoading = false;
        });
      }
    }
  }

  // Fetch transaction dates for the month to show dots on calendar (OPTIMIZED - Single API call!)
  Future<void> _fetchTransactionDates(DateTime date) async {
    if (!mounted) return;

    // Create cache key for this month
    final cacheKey = '${date.year}-${date.month}';

    // Check cache first
    if (_transactionDatesCache.containsKey(cacheKey) &&
        _cacheTimestamps.containsKey(cacheKey)) {
      final cacheTime = _cacheTimestamps[cacheKey]!;
      if (DateTime.now().difference(cacheTime) < _cacheValidDuration) {
        print('🔍 Cache hit! Using cached transaction dates for $cacheKey');
        setState(() {
          _transactionDates = _transactionDatesCache[cacheKey]!;
        });
        return;
      }
    }

    print(
        '🔍 Cache miss or expired. Fetching fresh transaction dates for $cacheKey');

    setState(() {
      _isLoadingTransactionDates = true;
    });

    try {
      final sharedRepo = locator<SharedRepository>();
      final token = sharedRepo.getToken();
      final centerId = sharedRepo.getCenterId();

      if (centerId.isEmpty || token == null) {
        throw Exception('User not logged in or Center ID not available');
      }

      final apiService = ApiService(baseUrl: Endpoints.baseUrl);

      // 🚀 NEW: Single API call to get all transaction dates for the month!
      final response = await apiService.get(
        '${Endpoints.transactions}/center/$centerId/transaction-dates',
        token: token,
        queryParameters: {
          'year': date.year.toString(),
          'month': date.month.toString(),
        },
      );

      print('🔍 Transaction dates response: $response');

      if (response is Map && response['success'] == true) {
        final data = response['data'];
        if (data is List) {
          // Convert date strings to ISO format for calendar
          final transactionDatesList = data
              .map((dateStr) {
                try {
                  // Convert YYYY-MM-DD to ISO format
                  final parsedDate = DateTime.parse(dateStr);
                  return parsedDate.toIso8601String();
                } catch (e) {
                  print('Error parsing date $dateStr: $e');
                  return null;
                }
              })
              .where((date) => date != null)
              .cast<String>()
              .toList();

          if (mounted) {
            // Update cache
            _transactionDatesCache[cacheKey] = transactionDatesList;
            _cacheTimestamps[cacheKey] = DateTime.now();

            setState(() {
              _transactionDates = transactionDatesList;
            });
            print('🔍 Transaction dates for calendar: $_transactionDates');
            print(
                '🔍 Cache updated for $cacheKey with ${transactionDatesList.length} dates');
          }
        } else {
          print('🔍 No transaction dates found for this month');
          if (mounted) {
            setState(() {
              _transactionDates = [];
            });
          }
        }
      } else {
        throw Exception('Failed to fetch transaction dates: Invalid response');
      }
    } catch (e) {
      print('Error fetching transaction dates: $e');
      if (mounted) {
        setState(() {
          _transactionDates = [];
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingTransactionDates = false;
        });
      }
    }
  }

  // Helper method to get proper coach display name
  String _getCoachDisplayName(String coachDisplayName) {
    if (coachDisplayName.isEmpty ||
        coachDisplayName == 'Unknown Coach' ||
        coachDisplayName == 'N/A' ||
        coachDisplayName == 'null') {
      return 'Coach'; // Just show "Coach" instead of "Unknown Coach"
    }
    return coachDisplayName;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: RefreshIndicator(
        onRefresh: () async {
          await _fetchMonthlyTotal(_focusday);
          await _fetchDailyTransactions(_selectedDay!);
        },
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Padding(
            padding: EdgeInsets.only(left: 19.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  height: 85.h,
                ),
                customBackButton(),
                Row(
                  children: [
                    customtext(
                      context: context,
                      newYear: 'Transaction',
                      font: 30.sp,
                      weight: FontWeight.w500,
                    ),
                  ],
                ),
                SizedBox(height: 16.h),
                Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.chevron_left),
                      onPressed: _previousMonth,
                    ),
                    Expanded(
                      child: Column(
                        children: [
                          customtext(
                            context: context,
                            newYear: DateFormat.yMMMM().format(_focusday),
                            font: 18.sp,
                            weight: FontWeight.w500,
                          ),
                          SizedBox(height: 4.h),
                          if (_isLoadingMonthlyTotal)
                            const CircularProgressIndicator()
                          else if (_monthlyTotalErrorMessage.isNotEmpty)
                            Text(
                              _monthlyTotalErrorMessage,
                              style:
                                  TextStyle(color: Colors.red, fontSize: 12.sp),
                            )
                          else
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                customtext(
                                  context: context,
                                  newYear: "HKD",
                                  font: 20.sp,
                                  weight: FontWeight.bold,
                                  color: AppPallete.secondaryColor,
                                ),
                                SizedBox(width: 4.w),
                                customtext(
                                  context: context,
                                  newYear:
                                      '${(_monthlyTotal * 25).toStringAsFixed(0)}',
                                  font: 20.sp,
                                  weight: FontWeight.bold,
                                  color: AppPallete.secondaryColor,
                                ),
                              ],
                            ),
                        ],
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.chevron_right),
                      onPressed: _nextMonth,
                    ),
                  ],
                ),
                SizedBox(height: 16.h),
                CustomCalendar(
                    focusedDay: _focusday,
                    selectedDay: _selectedDay,
                    onPageChanged: _onPageChanged,
                    onDaySelected: _onSelectedDay,
                    eventDates: _transactionDates),
                SizedBox(
                  height: 32.h,
                ),
                customtext(
                    context: context,
                    newYear:
                        'Enrolment this day - ${formatDate(_selectedDay ?? DateTime.now())}',
                    font: 20.sp,
                    weight: FontWeight.w500),
                SizedBox(
                  height: 18.h,
                ),
                _buildDailyTransactionsList(),
                SizedBox(height: 50.h), // Add padding at the bottom
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDailyTransactionsList() {
    if (_isDailyLoading) {
      return Center(
        child: Padding(
          padding: EdgeInsets.all(20.h),
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_dailyErrorMessage.isNotEmpty) {
      return Center(
        child: Padding(
          padding: EdgeInsets.all(20.h),
          child: Column(
            children: [
              Text(_dailyErrorMessage, style: TextStyle(color: Colors.red)),
              SizedBox(height: 10.h),
              ElevatedButton(
                onPressed: () => _fetchDailyTransactions(_selectedDay!),
                child: Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    if (_dailyTransactions.isEmpty) {
      return Center(
        child: Padding(
          padding: EdgeInsets.all(20.h),
          child: Column(
            children: [
              Icon(Icons.receipt_long, size: 50, color: Colors.grey[400]),
              SizedBox(height: 10.h),
              Text(
                'No transactions for this day.',
                style: TextStyle(fontSize: 16.sp, color: Colors.grey[600]),
              ),
              SizedBox(height: 10.h),
              ElevatedButton(
                onPressed: () => _fetchDailyTransactions(_selectedDay!),
                child: Text('Refresh'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppPallete.secondaryColor,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: NeverScrollableScrollPhysics(),
      itemCount: _dailyTransactions.length,
      itemBuilder: (context, index) {
        final transaction = _dailyTransactions[index];
        return Padding(
          padding: EdgeInsets.only(bottom: 16.h, right: 19.w),
          child: _transactionCard(
            context: context,
            transactionComplete: _isTransactionCompleted(transaction.status),
            studentName: transaction.studentName,
            className: transaction.className,
            date: DateFormat('dd MMM, HH:mm')
                .format(transaction.createdAt.toLocal()),
            location: transaction.location,
            sen: transaction.isSen,
            rate: transaction.amount.toStringAsFixed(2),
            coach: _getCoachDisplayName(transaction.coachDisplayName),
            onTap: () {
              NavigatorService.pushNamed(AppRoutes.intracsaction,
                  arguments: transaction);
            },
          ),
        );
      },
    );
  }

  Widget _transactionCard(
      {required BuildContext context,
      required VoidCallback onTap,
      required bool transactionComplete,
      required String studentName,
      required String className,
      required String date,
      required String location,
      required bool sen,
      required String rate,
      required String coach}) {
    return InkWell(
      onTap: onTap,
      child: Container(
        width: 380.w,
        decoration: BoxDecoration(
            color: AppPallete.white,
            boxShadow: [shadow(blurRadius: 15, opacity: 0.1)],
            borderRadius: BorderRadius.circular(20.r)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              height: 41.h,
              width: double.infinity, // Make it fill the full width
              padding: EdgeInsets.symmetric(horizontal: 18.w),
              decoration: BoxDecoration(
                  color: transactionComplete
                      ? AppPallete.secondaryColor
                      : Colors.orange,
                  borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(20.r),
                      topRight: Radius.circular(20.r))),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  customtext(
                      context: context,
                      newYear: studentName,
                      font: 12.sp,
                      weight: FontWeight.w600,
                      color: AppPallete.white),
                  Row(
                    children: [
                      Icon(
                        transactionComplete
                            ? Icons.check_circle
                            : Icons.pending,
                        color: AppPallete.white,
                        size: 16.h,
                      ),
                      SizedBox(width: 4.w),
                      customtext(
                          context: context,
                          newYear: transactionComplete
                              ? 'Payment completed'
                              : 'Payment pending',
                          font: 12.sp,
                          weight: FontWeight.w600,
                          color: AppPallete.white)
                    ],
                  ),
                ],
              ),
            ),
            SizedBox(
              height: 16.h,
            ),
            Padding(
              padding: EdgeInsets.only(left: 16.w, right: 11.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  customtext(
                      context: context,
                      newYear: date,
                      font: 12.sp,
                      weight: FontWeight.w500),
                  Row(
                    children: [
                      customSvgPicture(
                          imagePath: ImagePath.locationSvg,
                          height: 16.67.h,
                          width: 11.67.w),
                      SizedBox(
                        width: 8.33.w,
                      ),
                      customtext(
                          context: context,
                          newYear: location,
                          font: 15.sp,
                          weight: FontWeight.w400,
                          color: AppPallete.wordsOfRequest)
                    ],
                  )
                ],
              ),
            ),
            SizedBox(
              height: 15.h,
            ),
            Padding(
              padding: EdgeInsets.only(left: 16.w),
              child: customtext(
                  context: context,
                  newYear: className,
                  font: 20.sp,
                  weight: FontWeight.w600),
            ),
            SizedBox(
              height: 11.h,
            ),
            Padding(
              padding: EdgeInsets.only(left: 16.w),
              child: Row(
                children: [
                  customtext(
                    context: context,
                    newYear: "HKD",
                    font: 20.sp,
                    weight: FontWeight.w500,
                    color: AppPallete.secondaryColor,
                  ),
                  SizedBox(width: 4.w),
                  customtext(
                    context: context,
                    newYear: "${(double.parse(rate) * 25).toStringAsFixed(0)}",
                    font: 20.sp,
                    weight: FontWeight.w500,
                    color: AppPallete.secondaryColor,
                  ),
                ],
              ),
            ),
            SizedBox(
              height: 11.h,
            ),
            Padding(
              padding: EdgeInsets.only(left: 16.w, right: 15.w, bottom: 16.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  sen
                      ? Container(
                          padding: EdgeInsets.symmetric(
                              horizontal: 8.w, vertical: 4.h),
                          decoration: BoxDecoration(
                            color: AppPallete.paleGrey,
                            borderRadius: BorderRadius.circular(12.r),
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.star, size: 12.h, color: Colors.amber),
                              SizedBox(width: 4.w),
                              customtext(
                                context: context,
                                newYear: 'SEN support',
                                font: 12.sp,
                                weight: FontWeight.w500,
                              ),
                            ],
                          ),
                        )
                      : SizedBox(),
                  customtext(
                      context: context,
                      newYear: coach == 'Coach' ? 'by Coach' : 'by $coach',
                      font: 12.sp,
                      weight: FontWeight.w500)
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
