import 'package:class_z/core/imports.dart';

class InvoiceSection extends StatelessWidget {
  final List<DataRow> rows;
  final double subtotal;

  const InvoiceSection({
    Key? key,
    required this.rows,
    required this.subtotal,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Initialize SizeConfig if not already initialized
    if (!SizeConfig.isInitialized) {
      SizeConfig.init(context);
    }

    return Column(
      children: [
        InvoiceWidget(rows: rows),
        Sized<PERSON>ox(height: 9.h),
        customDivider(width: 406.w, padding: 12.w),
        SizedBox(height: 12.h),
        Padding(
          padding: EdgeInsets.only(right: 56.w),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              customtext(
                  context: context,
                  newYear: "Total",
                  font: 15.sp,
                  weight: FontWeight.w500),
              SizedBox(width: 8.w),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  customtext(
                    context: context,
                    newYear: "HKD",
                    font: 15.sp,
                    weight: FontWeight.w500,
                  ),
                  SizedBox(width: 4.w),
                  customtext(
                    context: context,
                    newYear: "${(subtotal * 25).toStringAsFixed(0)}",
                    font: 15.sp,
                    weight: FontWeight.w500,
                  ),
                ],
              )
            ],
          ),
        ),
      ],
    );
  }
}

class InvoiceWidget extends StatelessWidget {
  final List<DataRow> rows;

  static List<DataColumn> columns = [
    DataColumn(
        label: Text(
      'Item',
      style: TextStyle(fontSize: 13.sp, fontWeight: FontWeight.w500),
    )),
    DataColumn(
        label: Text(
      'Student',
      style: TextStyle(fontSize: 13.sp, fontWeight: FontWeight.w500),
    )),
    DataColumn(
        label: Text(
      'Rate',
      style: TextStyle(fontSize: 13.sp, fontWeight: FontWeight.w500),
    )),
    DataColumn(
        label: Text(
      'Subtotal',
      style: TextStyle(fontSize: 13.sp, fontWeight: FontWeight.w500),
    )),
  ];

  const InvoiceWidget({required this.rows, super.key});

  @override
  Widget build(BuildContext context) {
    // Initialize SizeConfig if not already initialized
    if (!SizeConfig.isInitialized) {
      SizeConfig.init(context);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        DataTable(
          columnSpacing: 35.w,
          decoration: BoxDecoration(
            border: Border.all(width: 0, color: Colors.transparent),
          ),
          columns: columns,
          rows: rows,
        ),
      ],
    );
  }
}
