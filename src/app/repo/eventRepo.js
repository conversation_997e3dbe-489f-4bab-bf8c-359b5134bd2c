const EventModel = require("../models/eventModel");

class EventRepository {
  async saveMany(events) {
    try {
      console.log("Saving events:", JSON.stringify(events, null, 2)); // Log the events being saved
      const result = await EventModel.insertMany(events);
      // console.log("Events saved:", JSON.stringify(result, null, 2));
      return result; // Return the saved events for further processing if needed
    } catch (error) {
      throw new Error("Error saving events: " + error.message);
    }
  }

  async getEvents(filter) {
    if (!filter || Object.keys(filter).length === 0) {
      throw new Error("Filter should not be empty");
    }

    try {
      const events = await EventModel.find(filter).populate([
        {
          path: "classId",
          populate: {
            path: "center",
            select: "displayName address"
          }
        },
        { path: "coachId", select: "displayName" },
        { path: "dateId", select: "startTime endTime durationMinutes weekDay repeat numberOfStudent charge languageOptions students minimumStudent" }, // Added schedule-specific fields
      ]);
      console.log(events);
      return events;
    } catch (error) {
      throw new Error(`Error retrieving events: ${error.message}`);
    }
  }
  async getEventDates(filter) {
    try {
      // Ensure centerId is an ObjectId if present
      const mongoose = require("mongoose");
      const match = { ...filter };
      if (match.centerId && typeof match.centerId === "string" && mongoose.Types.ObjectId.isValid(match.centerId)) {
        match.centerId = new mongoose.Types.ObjectId(match.centerId);
      }
      // Build aggregation pipeline
      const pipeline = [];
      if (Object.keys(match).length > 0) {
        pipeline.push({ $match: match });
      }
      pipeline.push({ $group: { _id: "$date" } });
      pipeline.push({ $project: { _id: 0, date: "$_id" } });
      // Use allowDiskUse for large collections
      const results = await EventModel.aggregate(pipeline).allowDiskUse(true).exec();
      return results.map(r => r.date);
    } catch (error) {
      throw new Error(`Error retrieving events: ${error.message}`);
    }
  }
  async getEventDatesByClassId(classId) {
    try {
      return await EventModel.find({ classId: classId }).populate("dateId");
      // .populate([
      //   { path: "classId" },
      //   { path: "coachId", select: "displayName" },
      // ]);
    } catch (error) {
      throw new Error(`Error retrieving events: ${error.message}`);
    }
  }
  async getEventById(eventId) {
    try {
      console.log("hi me");
      const event = await EventModel.findById(eventId).populate({
        path: "classId",
        populate: [
          {
            path: "coach",
            select: "displayName",
          },
          {
            path: "center",
            select: "displayName address",
          },
          {
            path: "student",
          },
        ],
      });
      if (!event) {
        throw new Error("No event found with the provided event ID");
      }
      return event;
    } catch (error) {
      throw new Error("Error retrieving event: " + error.message);
    }
  }
  async getByIdWithoutPopulate(eventId){
    return await EventModel.findById(eventId);
  }
  async getEventsByCenterIdAndDate(centerId, eventDate) {
    try {
      const date = new Date(eventDate).toISOString().split("T")[0]; // Ensure the date is in ISO format without time
      return await EventModel.find({
        centerId,
        date: { $eq: new Date(date + "T00:00:00.000Z") },
      }).populate({
        path: "classId",
        select: "mainImage numberOfStudent student",
      });
    } catch (error) {
      throw new Error(
        "Error retrieving events by center ID and date: " + error.message
      );
    }
  }

  async deleteEventById(eventId) {
    try {
      const result = await EventModel.deleteOne({ _id: eventId });

      if (result.deletedCount === 0) {
        throw new Error("No event found with the provided event ID");
      }

      return result;
    } catch (error) {
      throw new Error("Error deleting event: " + error.message);
    }
  }

  async deleteManyByClassId(classId) {
    try {
      const result = await EventModel.deleteMany({ classId });
      if (result.deletedCount === 0) {
        throw new Error("No events found with the provided class ID");
      }
      return result;
    } catch (error) {
      throw new Error("Error deleting events by class ID: " + error.message);
    }
  }

  async deleteManyByCenterId(centerId) {
    try {
      const result = await EventModel.deleteMany({ centerId });
      if (result.deletedCount === 0) {
        throw new Error("No events found with the provided center ID");
      }
      return result;
    } catch (error) {
      throw new Error("Error deleting events by center ID: " + error.message);
    }
  }

  async getPastEventsByClassId(classId) {
    try {
      const today = new Date();
      // Set time to the beginning of today to ensure all of today's past events are included if needed,
      // or end of yesterday if only strictly past days.
      // For "pending review", usually it means sessions that have concluded.
      today.setHours(0, 0, 0, 0);

      return await EventModel.find({
        classId: classId,
        date: { $lt: today }, // Events with a date strictly less than today
      })
        .populate([
          { path: "classId" }, // Populate class details if needed later
          { path: "coachId", select: "displayName" }, // Populate coach details if needed
        ])
        .sort({ date: -1 }); // Optional: sort by most recent past event first
    } catch (error) {
      console.error("Error in EventRepository getPastEventsByClassId:", error);
      throw new Error(
        "Error retrieving past events by classId: " + error.message
      );
    }
  }

  async getPastEventsByClassIds(classIds) {
    // New method for batch fetching
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0); // Consider events strictly before today

      // Ensure classIds are ObjectIds if your schema stores them that way
      // This step might need to be more robust depending on how IDs are passed
      const mongoose = require("mongoose");
      const objectIdClassIds = classIds
        .map((id) => {
          if (mongoose.Types.ObjectId.isValid(id) && typeof id === "string") {
            return new mongoose.Types.ObjectId(id);
          } else if (
            id &&
            typeof id.toString === "function" &&
            mongoose.Types.ObjectId.isValid(id.toString())
          ) {
            return new mongoose.Types.ObjectId(id.toString());
          }
          console.warn(
            `[EventRepository] Invalid ID format in classIds for getPastEventsByClassIds: ${id}`
          );
          return null;
        })
        .filter((id) => id !== null);

      if (objectIdClassIds.length === 0) {
        return new Map(); // No valid class IDs to query
      }

      const events = await EventModel.find({
        classId: { $in: objectIdClassIds },
        date: { $lt: today },
      })
        .select("classId date name coachId otherNecessaryFields") // Specify fields needed by ReviewUseCase
        .populate({ path: "coachId", select: "displayName" }) // Example population
        .lean(); // Use .lean() for performance

      const eventsMap = new Map();
      for (const event of events) {
        if (event.classId) {
          // Ensure event.classId exists
          const classIdStr = event.classId.toString();
          if (!eventsMap.has(classIdStr)) {
            eventsMap.set(classIdStr, []);
          }
          eventsMap.get(classIdStr).push(event);
        }
      }
      return eventsMap;
    } catch (error) {
      console.error("Error in EventRepository getPastEventsByClassIds:", error);
      throw new Error(
        "Error fetching past events by class IDs: " + error.message
      );
    }
  }

  async find(query) {
    try {
      return await EventModel.find(query).lean();
    } catch (error) {
      console.error("Error in EventRepository find:", error);
      throw new Error("Error finding events: " + error.message);
    }
  }

  // Admin Controller required methods
  async count(query) {
    try {
      return await EventModel.countDocuments(query);
    } catch (error) {
      console.error("Error in EventRepository count:", error);
      throw new Error("Error counting events: " + error.message);
    }
  }

  async findWithPagination(query, options = {}, skip = 0, limit = 10) {
    try {
      let queryBuilder = EventModel.find(query);

      if (options.populate) {
        queryBuilder = queryBuilder.populate(options.populate);
      }

      if (options.sort) {
        queryBuilder = queryBuilder.sort(options.sort);
      }

      return await queryBuilder.skip(skip).limit(limit);
    } catch (error) {
      console.error("Error in EventRepository findWithPagination:", error);
      throw new Error("Error finding events with pagination: " + error.message);
    }
  }

  async update(eventId, updateData) {
    try {
      const result = await EventModel.findByIdAndUpdate(eventId, updateData, {
        new: true,
        runValidators: true,
      });

      if (!result) {
        throw new Error("No event found with the provided event ID");
      }

      return result;
    } catch (error) {
      console.error("Error in EventRepository update:", error);
      throw new Error("Error updating event: " + error.message);
    }
  }

  async findById(eventId) {
    try {
      const event = await EventModel.findById(eventId);
      if (!event) {
        throw new Error("No event found with the provided event ID");
      }
      return event;
    } catch (error) {
      console.error("Error in EventRepository findById:", error);
      throw new Error("Error finding event by ID: " + error.message);
    }
  }

  async updateCoachForClass(classId, coachId) {
    try {
      console.log(`🔄 Updating coach for all events of class ${classId} to coach ${coachId}`);
      const result = await EventModel.updateMany(
        { classId: classId },
        { coachId: coachId }
      );
      console.log(`✅ Updated ${result.modifiedCount} events with new coach`);
      return result;
    } catch (error) {
      console.error("Error in EventRepository updateCoachForClass:", error);
      throw new Error("Error updating coach for class events: " + error.message);
    }
  }
  async findWithPopulate(query, populate) {
    return await EventModel.find(query).populate(populate);
  }

  /**
   * Delete all events by dateId
   * @param {string} dateId
   * @returns {Promise<Object>} The result of the deleteMany operation
   */
  async deleteManyByDateId(dateId) {
    try {
      const result = await EventModel.deleteMany({ dateId });
      return result;
    } catch (error) {
      throw new Error('Error deleting events by dateId: ' + error.message);
    }
  }
}

module.exports = EventRepository;
