class AnnouncementRepository {
  constructor(announcementModel) {
    this.announcementModel = announcementModel;
  }
  async save(data) {
    try {
      console.log(data);
      const announcement = new this.announcementModel(data);
      return await announcement.save();
    } catch (error) {
      throw ("Error creating announcement:", error);
    }
  }
  async getAnnouncementByClassId(classId) {
    try {
      const announcement = this.announcementModel
        .findOne({
          classId: classId,
        })
        .populate({
          path: "classId",
          select: "center student numberOfStudent",
          populate: {
            path: "center",
            select: "displayName",
          },
          populate: {
            path: "coach",
            select: "displayName",
          },
          populate: {
            path: "student", // Populate the student field
            select: "fullname mainImage", // Select the necessary fields from the student model (adjust as needed)
          },
        })
        .populate({
          path: "slotId",
          select: "date startTime endTime durationMinutes weekDay repeat numberOfStudent charge languageOptions students minimumStudent",
          populate: {
            path: "students",
            select: "fullname mainImage"
          }
        });

      return announcement;
    } catch (error) {
      throw ("Error fetching announcement:", error);
    }
  }
  async getById(announcementId) {
    try {
      const announcement = await this.announcementModel
        .findById(announcementId)
        .populate({
          path: "classId",
          select: "mainImage center coach classProviding",
          populate: [
            {
              path: "center",
              select: "displayName",
            },
            {
              path: "coach",
              select: "displayName",
            },
          ],
        })
        .populate({
          path: "slotId",
          select: "date startTime endTime durationMinutes weekDay repeat numberOfStudent charge languageOptions students minimumStudent",
          populate: {
            path: "students",
            select: "fullname mainImage"
          }
        });

      console.log(`announcement :${announcement}`);
      return announcement;
    } catch (error) {
      throw ("Error saving announcement:", error);
    }
  }
  async getByClassId(classId) {
    try {
      return await this.announcementModel
        .findOne({ classId })
        .populate({
          path: "slotId",
          select: "date startTime endTime durationMinutes weekDay repeat numberOfStudent charge languageOptions students minimumStudent",
          populate: {
            path: "students",
            select: "fullname mainImage"
          }
        });
    } catch (error) {
      throw ("Error fetching announcement:", error);
    }
  }
  async update(announcementId, data) {
    try {
      console.log(data);
      return await this.announcementModel
        .findByIdAndUpdate(announcementId, data, {
          new: true,
        })
        .populate({
          path: "classId",
        })
        .populate({
          path: "slotId",
          select: "date startTime endTime durationMinutes weekDay repeat numberOfStudent charge languageOptions students minimumStudent",
          populate: {
            path: "students",
            select: "fullname mainImage"
          }
        });
    } catch (error) {
      throw ("Error saving announcement:", error);
    }
  }
  async getAnnouncementBySlotId(slotId) {
    try {
      const announcement = await this.announcementModel
        .findOne({ slotId })
        .populate({
          path: "slotId",
          select: "date startTime endTime durationMinutes weekDay repeat numberOfStudent charge languageOptions students minimumStudent",
          populate: {
            path: "students",
            select: "fullname mainImage"
          }
        });
      return announcement;
    } catch (error) {
      throw ("Error fetching announcement by slotId:", error);
    }
  }
}

module.exports = AnnouncementRepository;
