const mongoose = require('mongoose');
const { Types } = mongoose;
const TransactionModel = require("../models/transactionModel");
const CoachModel = require("../models/coachModel");

class TransactionRepo {
  async save(transaction) {
    const transactionRecord = new TransactionModel(transaction);
    await transactionRecord.save();
    return transactionRecord;
  }

  async getTransactionById(id) {
    return await TransactionModel.findOne({ id });
  }

  async getTransactionByUserId(id) {
    return await TransactionModel.find({ id });
  }

  async getMonthlyTotal(centerId, year, month) {
    const startDate = new Date(year, month - 1, 1);
    const endDate = new Date(year, month, 0, 23, 59, 59);

    console.log(`Looking for monthly transactions between ${startDate} and ${endDate} for center ${centerId}`);

    // First, find all classes that belong to this center
    const ClassModel = require("../models/classModel");
    const classes = await ClassModel.find({ center: new Types.ObjectId(centerId) });
    
    if (classes.length === 0) {
      console.log(`No classes found for center ${centerId}`);
      return 0;
    }
    
    const classIds = classes.map(c => c._id);
    console.log(`Found ${classes.length} classes for center ${centerId}`);

    // Now, find all orders for these classes
    const OrderModel = require("../models/ordersModel");
    const orders = await OrderModel.find({
      classId: { $in: classIds },
      paid: true,
      createdAt: {
        $gte: startDate,
        $lte: endDate
      }
    });
    
    console.log(`Found ${orders.length} orders for classes in center ${centerId} for month ${month}/${year}`);
    
    // Calculate total from orders
    let total = orders.reduce((sum, order) => sum + (order.amount || 0), 0);
    console.log(`Total from orders: ${total}`);
    
    // Also find transactions directly linked to these orders
    const orderIds = orders.map(order => order._id);
    const transactions = await TransactionModel.find({
      orderId: { $in: orderIds },
      status: 'Completed',
      type: 'purchase',
      createdAt: {
        $gte: startDate,
        $lte: endDate
      }
    });
    
    console.log(`Found ${transactions.length} transactions linked to orders for center ${centerId}`);
    
    // If we found transactions, use their sum instead (more accurate)
    if (transactions.length > 0) {
      const transactionTotal = transactions.reduce((sum, tx) => sum + (tx.amount || 0), 0);
      console.log(`Total from transactions: ${transactionTotal}`);
      
      // Use the higher value between orders and transactions to avoid missing any
      total = Math.max(total, transactionTotal);
    }
    
    // As a fallback, also check for transactions from coaches at this center
    const coaches = await CoachModel.find({ center: new Types.ObjectId(centerId) }).select('baseUser');
    if (coaches && coaches.length > 0) {
      const coachUserIds = coaches.map(coach => coach.baseUser);
      
      // Get all transactions for these coaches
      const coachTransactions = await TransactionModel.find({
        userId: { $in: coachUserIds },
        status: 'Completed',
        type: 'purchase',
        createdAt: {
          $gte: startDate,
          $lte: endDate
        }
      });
      
      console.log(`Found ${coachTransactions.length} coach transactions for center ${centerId}`);
      
      if (coachTransactions.length > 0) {
        const coachTotal = coachTransactions.reduce((sum, tx) => sum + (tx.amount || 0), 0);
        console.log(`Total from coach transactions: ${coachTotal}`);
        
        // Add coach transactions that don't have an orderId (to avoid double counting)
        const coachTransactionsWithoutOrder = coachTransactions.filter(tx => !tx.orderId);
        const additionalTotal = coachTransactionsWithoutOrder.reduce((sum, tx) => sum + (tx.amount || 0), 0);
        console.log(`Additional total from coach transactions without order: ${additionalTotal}`);
        
        total += additionalTotal;
      }
    }
    
    // Finally, look for any other transactions linked to this center that might have been missed
    const allDailyTransactions = await this.getDailyTransactionsForCenter(centerId, startDate.toISOString().split('T')[0]);
    
    // Count only transactions that haven't been counted yet
    const allTransactionIds = [...transactions.map(t => t._id.toString()), ...orderIds.map(id => id.toString())];
    const missedTransactions = allDailyTransactions.filter(t => !allTransactionIds.includes(t._id.toString()));
    
    if (missedTransactions.length > 0) {
      const missedTotal = missedTransactions.reduce((sum, tx) => sum + (tx.amount || 0), 0);
      console.log(`Found ${missedTransactions.length} missed transactions with total: ${missedTotal}`);
      total += missedTotal;
    }
    
    console.log(`Final monthly total for center ${centerId}: ${total}`);
    return total;
  }

  async getTotalEarnings(centerId) {
    console.log(`Getting total earnings for center ${centerId}`);

    // First, find all classes that belong to this center
    const ClassModel = require("../models/classModel");
    const classes = await ClassModel.find({ center: new Types.ObjectId(centerId) });
    
    if (classes.length === 0) {
      console.log(`No classes found for center ${centerId}`);
      return 0;
    }
    
    const classIds = classes.map(c => c._id);
    console.log(`Found ${classes.length} classes for center ${centerId}`);

    // Now, find all paid orders for these classes
    const OrderModel = require("../models/ordersModel");
    const orders = await OrderModel.find({
      classId: { $in: classIds },
      paid: true,
    });
    
    console.log(`Found ${orders.length} total orders for classes in center ${centerId}`);
    
    // Calculate total from orders
    let total = orders.reduce((sum, order) => sum + (order.amount || 0), 0);
    console.log(`Total from orders: ${total}`);
    
    // As a fallback, use transactions (more accurate)
    const orderIds = orders.map(order => order._id);
    const transactions = await TransactionModel.find({
      orderId: { $in: orderIds },
      status: 'Completed',
      type: 'purchase',
    });
    
    if (transactions.length > 0) {
      const transactionTotal = transactions.reduce((sum, tx) => sum + (tx.amount || 0), 0);
      console.log(`Total from transactions: ${transactionTotal}`);
      total = Math.max(total, transactionTotal);
    }
    
    // Also consider transactions from coaches at this center
    const coaches = await CoachModel.find({ center: new Types.ObjectId(centerId) }).select('baseUser');
    if (coaches && coaches.length > 0) {
      const coachUserIds = coaches.map(coach => coach.baseUser);
      
      const coachTransactions = await TransactionModel.find({
        userId: { $in: coachUserIds },
        status: 'Completed',
        type: 'purchase',
      });
      
      console.log(`Found ${coachTransactions.length} total coach transactions for center ${centerId}`);
      
      if (coachTransactions.length > 0) {
        const coachTransactionsWithoutOrder = coachTransactions.filter(tx => !tx.orderId);
        const additionalTotal = coachTransactionsWithoutOrder.reduce((sum, tx) => sum + (tx.amount || 0), 0);
        console.log(`Additional total from coach transactions without order: ${additionalTotal}`);
        total += additionalTotal;
      }
    }
    
    console.log(`Final total earnings for center ${centerId}: ${total}`);
    return total;
  }

  async getDailyTransactionsForCenter(centerId, dateString) {
    // Calculate start and end of the given date
    const startDate = new Date(dateString); // Assumes dateString is YYYY-MM-DD
    startDate.setUTCHours(0, 0, 0, 0); // Start of the day in UTC

    const endDate = new Date(dateString);
    endDate.setUTCHours(23, 59, 59, 999); // End of the day in UTC

    console.log(`Looking for transactions between ${startDate} and ${endDate} for center ${centerId}`);

    // First, let's find all classes that belong to this center
    const ClassModel = require("../models/classModel");
    const classes = await ClassModel.find({ center: new Types.ObjectId(centerId) });
    
    if (classes.length === 0) {
      console.log(`No classes found for center ${centerId}`);
      return [];
    }
    
    const classIds = classes.map(c => c._id);
    console.log(`Found ${classes.length} classes for center ${centerId}: ${classIds}`);

    // Now, let's find all orders for these classes
    const OrderModel = require("../models/ordersModel");
    const orders = await OrderModel.find({
      classId: { $in: classIds },
      createdAt: {
        $gte: startDate,
        $lte: endDate
      }
    }).populate('childId');
    
    console.log(`Found ${orders.length} orders for classes in center ${centerId} on date ${dateString}`);
    
    if (orders.length === 0) {
      // Try to find orders by date field instead of createdAt
      const ordersByDate = await OrderModel.find({
        classId: { $in: classIds },
        date: {
          $elemMatch: {
            $gte: startDate,
            $lte: endDate
          }
        }
      }).populate('childId');
      
      console.log(`Found ${ordersByDate.length} orders by date field for classes in center ${centerId}`);
      
      if (ordersByDate.length > 0) {
        orders.push(...ordersByDate);
      }
    }
    
    // Step 1: Find all coach baseUser IDs for the given centerId
    const coaches = await CoachModel.find({ center: new Types.ObjectId(centerId) }).select('baseUser displayName mainImage'); // Also select displayName and mainImage for the coach
    if (coaches && coaches.length > 0) {
      console.log(`Found ${coaches.length} coaches with baseUser IDs: ${coaches.map(coach => coach.baseUser)}`);
      
      // Create a map of baseUser ID to coach details for easy lookup later
      const coachDetailsMap = new Map();
      coaches.forEach(coach => {
        coachDetailsMap.set(coach.baseUser.toString(), {
          displayName: coach.displayName,
          mainImage: coach.mainImage
        });
      });
      
      // Step 2: Find transactions for these coachUserIds within the date range
      const coachUserIds = coaches.map(coach => coach.baseUser);
      const transactions = await TransactionModel.find({
        $or: [
          { userId: { $in: coachUserIds } }, // userId here is the coach's baseUser ID
          { type: 'purchase' } // Include all purchase transactions
        ],
        createdAt: {
          $gte: startDate,
          $lte: endDate
        }
      }).sort({ createdAt: -1 }); // Sort by newest first
      
      console.log(`Found ${transactions.length} transactions for the date ${dateString}`);
      
      // Add transactions to the list of items to process
      for (const tx of transactions) {
        // Find the related order
        const relatedOrder = orders.find(o => o._id.toString() === tx.orderId?.toString());
        
        if (relatedOrder) {
          console.log(`Found related order for transaction ${tx._id}: ${relatedOrder._id}`);
        } else {
          // Try to find the order directly
          const order = await OrderModel.findOne({
            _id: tx.orderId
          }).populate('childId');
          
          if (order) {
            console.log(`Found order by ID for transaction ${tx._id}: ${order._id}`);
            orders.push(order);
          }
        }
      }
    }
    
    // Now process all orders to create transaction records
    const ChildModel = require("../models/childModel");
    const augmentedTransactions = [];
    
    for (const order of orders) {
      try {
        let studentName = "Unknown Student";
        let className = "Unknown Class";
        let location = "Unknown Location";
        let isSen = false;
        
        // Get child details
        if (order.childId) {
          if (typeof order.childId === 'object' && order.childId.fullname) {
            studentName = order.childId.fullname;
            isSen = order.childId.sen || false;
          } else {
            const child = await ChildModel.findById(order.childId);
            if (child) {
              studentName = child.fullname || "Unknown Student";
              isSen = child.sen || false;
            }
          }
        }
        
        // Get class details
        const classInfo = await ClassModel.findById(order.classId).populate('center');
        if (classInfo) {
          className = classInfo.classProviding || "Unknown Class";
          location = classInfo.center?.displayName || "Unknown Location";
        }
        
        // Find existing transaction for this order
        const existingTransaction = await TransactionModel.findOne({ orderId: order._id });
        
        if (existingTransaction) {
          // Use the existing transaction data
          augmentedTransactions.push({
            ...existingTransaction.toObject(),
            studentName: studentName,
            className: className,
            location: location,
            isSen: isSen || order.sen || false,
            paymentStatus: existingTransaction.status || 'Completed',
            paid: order.paid || true
          });
        } else {
          // Create a synthetic transaction
          augmentedTransactions.push({
            _id: order._id,
            transactionId: `order-${order._id}`,
            userId: order.userId,
            amount: order.amount,
            type: 'purchase',
            status: 'Completed',
            createdAt: order.createdAt,
            coachDisplayName: 'From Order', // We don't have coach info for direct orders
            coachMainImage: null,
            studentName: studentName,
            className: className,
            location: location,
            isSen: isSen || order.sen || false,
            paymentStatus: 'Completed',
            paid: order.paid || true
          });
        }
      } catch (error) {
        console.error(`Error processing order ${order._id}:`, error);
      }
    }

    console.log(`Returning ${augmentedTransactions.length} transactions for center ${centerId}`);
    return augmentedTransactions;
  }

  async getTransactionDatesForMonth(centerId, year, month) {
    const startDate = new Date(year, month - 1, 1);
    const endDate = new Date(year, month, 0, 23, 59, 59);

    console.log(`Looking for transaction dates between ${startDate} and ${endDate} for center ${centerId}`);

    // First, find all classes that belong to this center
    const ClassModel = require("../models/classModel");
    const classes = await ClassModel.find({ center: new Types.ObjectId(centerId) });

    if (classes.length === 0) {
      console.log(`No classes found for center ${centerId}`);
      return [];
    }

    const classIds = classes.map(c => c._id);
    console.log(`Found ${classes.length} classes for center ${centerId}`);

    // Find all orders for these classes in the date range
    const OrderModel = require("../models/ordersModel");
    const orders = await OrderModel.find({
      classId: { $in: classIds },
      createdAt: {
        $gte: startDate,
        $lte: endDate
      }
    });

    // Find all transactions for coaches at this center in the date range
    const coaches = await CoachModel.find({ center: new Types.ObjectId(centerId) }).select('baseUser');
    let transactions = [];

    if (coaches && coaches.length > 0) {
      const coachUserIds = coaches.map(coach => coach.baseUser);

      transactions = await TransactionModel.find({
        userId: { $in: coachUserIds },
        createdAt: {
          $gte: startDate,
          $lte: endDate
        }
      });
    }

    // Collect unique dates
    const uniqueDates = new Set();

    // Add dates from orders
    orders.forEach(order => {
      const dateString = order.createdAt.toISOString().split('T')[0]; // YYYY-MM-DD format
      uniqueDates.add(dateString);
    });

    // Add dates from transactions
    transactions.forEach(transaction => {
      const dateString = transaction.createdAt.toISOString().split('T')[0]; // YYYY-MM-DD format
      uniqueDates.add(dateString);
    });

    const result = Array.from(uniqueDates).sort();
    console.log(`Found ${result.length} unique transaction dates for center ${centerId}: ${result}`);

    return result;
  }
}

module.exports = TransactionRepo;
