
class AuthMiddleware {
  constructor(tokenService) {
    this.tokenService = tokenService;
  }

  authenticate(req, res, next) {
    try {
      console.log("PSFLKHSLKFJHLKSJHFLKJSFLKJSLKFJlk")
      const token = req.header('auth-token');
      console.log("🔐 Auth middleware - Token received:", token ? token.substring(0, 20) + "..." : "null");

      if (!token) {
        console.log("❌ No token provided");
        return res.status(401).json({ message: 'Access Denied' });
      }

      console.log("🔍 Validating token...");
      const verified = this.tokenService.validate(token);
      console.log("✅ Token validated successfully:", verified);

      req.user = { id: verified.id, type: verified.type };
      console.log("👤 Set req.user:", req.user);
      next();
    } catch (error) {
      console.log("❌ Token validation failed:", error.message);
      console.log("🔍 Error details:", error);
      res.status(400).json({ message: 'Invalid Token' });
    }
  }
}

module.exports = AuthMiddleware;
