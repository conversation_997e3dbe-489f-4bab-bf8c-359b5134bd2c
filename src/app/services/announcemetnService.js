class AnnouncementService {
  constructor(
    announcementRepository,
    classDateRepo,
    childRepo,
    sendAnnouncementNotification
  ) {
    this.announcementRepository = announcementRepository;
    this.classDateRepo = classDateRepo; // Handles slot (classDate) operations
    this.childRepo = childRepo; // Handles child/student operations
    this.sendAnnouncementNotification = sendAnnouncementNotification;
  }
  async save(data) {

    try {
      // Ensure that classDate (slot) exists
      const classDate = await this.classDateRepo.getById(data.slotId);
      if (!classDate) {
        throw new Error("No class date (slot) found by this id");
      }
   
      // Step 1: Prepare the data for saving
      const announcementData = {
        classId: classDate.classId,
        slotId: data.slotId,
        title: data.title || '',
        mainImage: data.mainImage || '',
        senderName: data.senderName || '',
        senderImage: data.senderImage || '',
        messages: [
          {
            message: data.message,
            messageImage: data.messageImage || null,
            createdAt: new Date(), // Automatically set the createdAt to the current date
          },
        ],
      };

      // Step 2: Fetch existing announcement by slotId
      let announcement = await this.announcementRepository.getAnnouncementBySlotId(data.slotId);

      if (announcement) {
        // If the announcement exists, push the new message into the messages array
        announcement.messages.push({
          message: data.message,
          messageImage: data.messageImage || null,
          createdAt: new Date(),
        });

        // Also update title and image, in case they changed
        announcement.title = data.title || '';
        announcement.mainImage = data.mainImage || '';
        announcement.senderName = data.senderName || '';
        announcement.senderImage = data.senderImage || '';

        // Save the updated announcement
        announcement = await this.announcementRepository.update(
          announcement._id,
          announcement
        );
      } else {
        // If no announcement exists, create a new one
        announcement = await this.announcementRepository.save(announcementData);
      }
      // Fetch students for the slot and get their parentIds
      console.log(`students : ${classDate.students}`);
      const studentIds = classDate.students || [];
      const parentIds = [];
      for (const studentId of studentIds) {
        const parentId = await this.childRepo.getParentByChildId(studentId);
        if (parentId) parentIds.push(parentId);
      }
      // Send notification to all parentIds
      await this.sendAnnouncementNotification.execute(announcement, parentIds);
      return true;
    } catch (error) {
      console.error("Error saving announcement:", error);
      throw new Error("Error saving announcement: " + error.message);
    }
  }

  async getAnnouncementByAnnouncementId(classId) {
    const announcement = await this.announcementRepository.getById(classId);

    return announcement;
  }
  async getAnnouncementByClassId(classId) {
    const announcement =
      await this.announcementRepository.getAnnouncementByClassId(classId);

    return announcement;
  }
  async getAnnouncementBySlotId(slotId) {
    console.log('hello')
    const announcement = await this.announcementRepository.getAnnouncementBySlotId(slotId);
    return announcement;
  }
}

module.exports = AnnouncementService;
