const express = require('express');
const router = express.Router();
const transactionController = require('../controllers/transactionController');
const authenticateToken = require('../../middleware/auth');

// Get monthly transaction total for a center
router.get('/center/:centerId/monthly-total', authenticateToken, transactionController.getMonthlyTotal);

// Get daily transactions for a center
router.get('/center/:centerId/daily', authenticateToken, transactionController.getDailyTransactionsForCenter);

// Get transaction dates for a month (for calendar dots)
router.get('/center/:centerId/transaction-dates', authenticateToken, transactionController.getTransactionDatesForMonth);

// Get total earnings for a center
router.get('/center/:centerId/total-earnings', authenticateToken, transactionController.getTotalEarnings);

module.exports = router;
