const express = require("express");
const router = express.Router();
const container = require("../../container/container");
const EventController = require("../controllers/eventController");

// Use eventService from the container
const eventService = container.eventService;
const eventController = new EventController(eventService);

// Route to create events from class data
router.post("/", (req, res) => eventController.createEvents(req, res));

// Route to fetch events
router.get("/", (req, res) => eventController.getEventsByFilter(req, res));

// Route to get event dates for a month (for calendar dots)
router.get("/dates", (req, res) => eventController.getEventDatesForMonth(req, res));

// Route to fetch events by classId
router.get("/class/:classId", (req, res) =>
  eventController.getEventByClassId(req, res)
);

// Route to get a specific event by event ID
router.get("/event/:eventId", (req, res) =>
  eventController.getEventById(req, res)
);

// Route to delete events by center ID
router.delete("/center/:centerId", (req, res) =>
  eventController.deleteEventsByCenterId(req, res)
);

// Route to delete events by class ID (Updated to handle refund/rearrange)
router.delete("/class/:classId", (req, res) =>
  eventController.deleteEventsByClassId(req, res)
);

// Route to delete a single event by event ID
router.delete("/event/:eventId", (req, res) =>
  eventController.deleteEventById(req, res)
);

// Route to get events by parentId
router.get("/parent/:parentId", (req, res) => eventController.getEventsByParentId(req, res));

module.exports = router;
