const express = require("express");
const router = express.Router();
const path = require("path");
const fs = require("fs");
const multer = require("multer");
const AuthMiddleware = require("../services/authMiddleware");

// Get dependencies
const TokenService = require("../services/tokenService");
const tokenService = new TokenService();
const authMiddleware = new AuthMiddleware(tokenService);

// Configure multer for memory storage (temporarily)
const upload = multer({
  storage: multer.memoryStorage(),
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB limit
  fileFilter: function (req, file, cb) {
    // Accept only images and specific document types
    const allowedMimes = [
      "image/jpeg",
      "image/png",
      "image/gif",
      "application/pdf",
    ];
    if (allowedMimes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(
        new Error(
          "Invalid file type. Only JPEG, PNG, GIF, and PDF are allowed."
        )
      );
    }
  },
});

/**
 * @route POST /api/upload/image
 * @desc Upload an image file and organize by user ID
 * @access Private (requires authentication)
 */
router.post(
  "/image",
  authMiddleware.authenticate.bind(authMiddleware),
  upload.single("image"),
  async (req, res) => {
    try {
      const userId = req.user.id;
      const imageType = req.body.imageType || "general"; // e.g., 'profile', 'mainImage', etc.
      const file = req.file;

      console.log(`Upload request received - User ID: ${userId}, Image Type: ${imageType}`);
      console.log(`File details:`, {
        originalname: file ? file.originalname : 'undefined',
        mimetype: file ? file.mimetype : 'undefined',
        size: file ? file.size : 'undefined'
      });

      if (!file) {
        console.error("Upload failed: No file in request");
        return res.status(400).json({ 
          success: false, 
          error: "No file uploaded" 
        });
      }

      // Create directory if it doesn't exist
      const userDir = path.join(__dirname, "../../../uploads", userId);
      console.log(`Creating directory (if needed): ${userDir}`);
      
      try {
        if (!fs.existsSync(userDir)) {
          fs.mkdirSync(userDir, { recursive: true });
          console.log(`Created new directory: ${userDir}`);
        } else {
          console.log(`Directory already exists: ${userDir}`);
        }
      } catch (dirError) {
        console.error(`Directory creation error: ${dirError.message}`);
        return res.status(500).json({ 
          success: false, 
          error: `Failed to create upload directory: ${dirError.message}` 
        });
      }

      // Generate stable filename
      const extension = path.extname(file.originalname).toLowerCase();
      // Use timestamp to ensure uniqueness if needed
      const timestamp = new Date().getTime();
      const filename = imageType === "general" 
        ? `${imageType}_${timestamp}${extension}` 
        : `${imageType}${extension}`;
      
      const filepath = path.join(userDir, filename);
      console.log(`Writing file to: ${filepath}`);

      try {
        // Write file buffer to disk
        fs.writeFileSync(filepath, file.buffer);
        console.log(`File written successfully: ${filepath}`);
      } catch (fileError) {
        console.error(`File write error: ${fileError.message}`);
        return res.status(500).json({ 
          success: false, 
          error: `Failed to save file: ${fileError.message}` 
        });
      }

      // Return the relative path to be stored in the database
      const relativePath = `/uploads/${userId}/${filename}`;
      const fullUrl = `https://api.classz.co${relativePath}`;
      
      console.log(`Upload successful - returning URL: ${fullUrl}`);
      
      res.json({
        success: true,
        url: relativePath,
        fullUrl: fullUrl,
        contentType: file.mimetype,
      });
    } catch (error) {
      console.error("Error uploading image:", error);
      res.status(500).json({ success: false, error: error.message });
    }
  }
);

/**
 * @route POST /api/upload/multiple
 * @desc Upload multiple image files
 * @access Private (requires authentication)
 */
router.post(
  "/multiple",
  authMiddleware.authenticate.bind(authMiddleware),
  upload.array("images", 10),
  async (req, res) => {
    try {
      const userId = req.user.id;
      const imageType = req.body.imageType || "general";
      const files = req.files;

      console.log(`Multiple upload request received - User ID: ${userId}, Image Type: ${imageType}`);
      console.log(`Number of files: ${files ? files.length : 0}`);

      if (!files || files.length === 0) {
        console.error("Multiple upload failed: No files in request");
        return res.status(400).json({ 
          success: false, 
          error: "No files uploaded" 
        });
      }

      // Create directory if it doesn't exist
      const userDir = path.join(__dirname, "../../../uploads", userId);
      console.log(`Creating directory (if needed): ${userDir}`);
      
      try {
        if (!fs.existsSync(userDir)) {
          fs.mkdirSync(userDir, { recursive: true });
          console.log(`Created new directory: ${userDir}`);
        } else {
          console.log(`Directory already exists: ${userDir}`);
        }
      } catch (dirError) {
        console.error(`Directory creation error: ${dirError.message}`);
        return res.status(500).json({ 
          success: false, 
          error: `Failed to create upload directory: ${dirError.message}` 
        });
      }

      // Process each file
      const uploadResults = files.map((file, index) => {
        console.log(`Processing file ${index+1}/${files.length}: ${file.originalname}`);
        
        const extension = path.extname(file.originalname).toLowerCase();
        const timestamp = new Date().getTime();
        const filename = `${imageType}_${index}_${timestamp}${extension}`;
        const filepath = path.join(userDir, filename);

        try {
          // Write file buffer to disk
          fs.writeFileSync(filepath, file.buffer);
          console.log(`File written successfully: ${filepath}`);
        } catch (fileError) {
          console.error(`Error writing file ${index+1}: ${fileError.message}`);
          throw fileError;
        }

        // Return file info
        const relativePath = `/uploads/${userId}/${filename}`;
        const fullUrl = `https://api.classz.co${relativePath}`;
        
        return {
          url: relativePath,
          fullUrl: fullUrl,
          contentType: file.mimetype,
        };
      });

      console.log(`Multiple upload successful - returning ${uploadResults.length} URLs`);
      
      res.json({
        success: true,
        files: uploadResults,
      });
    } catch (error) {
      console.error("Error uploading multiple images:", error);
      res.status(500).json({ success: false, error: error.message });
    }
  }
);

module.exports = router; 