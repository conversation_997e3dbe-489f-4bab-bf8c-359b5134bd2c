const mongoose = require("mongoose");
const Schema = mongoose.Schema;

const chatModel = new mongoose.Schema({
  message: { type: String, required: true },
  sender: {
    type: Schema.Types.ObjectId,
    required: true,
    refPath: "senderModel",
  },
  recipient: {
    type: Schema.Types.ObjectId,
    required: true,
    refPath: "recipientModel",
  },
  senderModel: {
    type: String,
    required: true,
    enum: ["user", "center", "coach"],
  },
  recipientModel: {
    type: String,
    required: true,
    enum: ["user", "center", "coach"],
  },
  timestamp: { type: Date, default: Date.now },
  status: {
    type: String,
    required: true,
    enum: ["sent", "delivered", "read"],
    default: "sent"
  },
  conversationId: {
    type: String,
    index: true
  },
  imageUrl: {
    type: String,
    required: false
  }
});

chatModel.index({ sender: 1, recipient: 1 });
chatModel.index({ recipient: 1, sender: 1 });
chatModel.index({ timestamp: -1 });
chatModel.index({ sender: 1, timestamp: -1 });
chatModel.index({ recipient: 1, timestamp: -1 });
chatModel.index({ status: 1 });

const ChatModel = mongoose.model("chat", chatModel);
module.exports = ChatModel;
