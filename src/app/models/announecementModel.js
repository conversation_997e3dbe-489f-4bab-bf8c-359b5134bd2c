const mongoose = require("mongoose");

const announcementSchema = new mongoose.Schema( // Fixed typo here
  {
    classId: {
      type: mongoose.Schema.Types.ObjectId, // Fixed Schema reference
      ref: "class", // Ensure this matches the class model name
      required: true,
    },
    slotId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "classDate",
      required: true, // Optional for backward compatibility
    },
    title: {
      type: String,
      default: "",
    },
    mainImage: {
      url: String,
      contentType: String,
    },
    senderName: {
      type: String,
      default: "",
    },
    senderImage: {
      url: String,
      contentType: String,
    },
    messages: [{
      message: { type: String, required: true },
      messageImage: {
        url: String,
        contentType: String,
      },
      createdAt: { type: Date, default: Date.now },
    }],
  },
  {
    timestamps: true,
  }
);

const Announcement = mongoose.model("announcement", announcementSchema); // Fixed model name
module.exports = Announcement;
