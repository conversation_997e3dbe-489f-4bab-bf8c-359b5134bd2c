class EventController {
  constructor(eventService) {
    this.eventService = eventService;
  }

  async createEvents(req, res) {
    try {
      const classModel = req.body;

      // Call the use case to create events
      await this.eventService.createEvents(classModel);

      // Return a success response
      res.status(201).json({ message: "Events created successfully" });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async getEventsByFilter(req, res) {
    const { date, coachId, centerId, classId } = req.query;

    // Build the filter object dynamically
    const filter = {};
    console.log(date);
    if (date) {
      const startOfDay = new Date(date + 'T00:00:00.000Z'); // Start of day in UTC
      const endOfDay = new Date(date + 'T23:59:59.999Z'); // End of day in UTC

      console.log('🔍 EVENT FILTER: Date range:', {
        inputDate: date,
        startOfDay: startOfDay.toISOString(),
        endOfDay: endOfDay.toISOString()
      });

      // Add date range filter to the query
      filter.date = {
        $gte: startOfDay,
        $lte: endOfDay,
      };
    }

    if (coachId) {
      filter.coachId = coachId; // Add coachId to filter if provided
    }
    if (centerId) {
      filter.centerId = centerId; // Add centerId to filter if provided
    }
    if (classId) {
      filter.classId = classId; // Add classId to filter if provided
    }
    console.log(filter);
    try {
      const events = await this.eventService.getEvents(filter);
      console.log(`🔍 EVENT FILTER: Found ${events.length} events`);
      if (events.length > 0) {
        console.log('🔍 EVENT FILTER: First event sample:', JSON.stringify({
          id: events[0]._id,
          classId: events[0].classId?._id,
          classAddress: events[0].classId?.address,
          centerDisplayName: events[0].classId?.center?.displayName,
          centerAddress: events[0].classId?.center?.address
        }, null, 2));
      }
      res.status(200).json(events);
    } catch (error) {
      console.error('❌ EVENT FILTER ERROR:', error.message);
      res.status(500).json({ error: error.message });
    }
  }

  async getEventById(req, res) {
    const { eventId } = req.params;
    try {
      const event = await this.eventService.getEventById(eventId);
      if (!event) {
        return res.status(404).json({ message: "Event not found" });
      }
      console.log("Fetched event:", event);
      res.status(200).json(event);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }
  async getEventByClassId(req, res) {
    try {
      console.log(req.params);
      const { classId } = req.params;
      const event = await this.eventService.getEventByClassId(classId);
      res.status(200).json(event);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }
  async getEventsByIdAndDate(req, res) {
    const { center, date } = req.query;
    try {
      console.log(center, date);
      const event = await this.getEventsByCenterIdAndDate.execute(center, date);
      res.status(200).json(event);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }
  async deleteEventById(req, res) {
    const { eventId } = req.params;
    console.log(eventId);
    try {
      await this.eventService.deleteEventById(eventId); // Updated reference
      res.status(204).send(); // No content
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  }

  async deleteEventsByClassId(req, res) {
    const { classId } = req.params;
    const data = req.body; // Extract cancelType from request body
    console.log('🚀 CANCEL REQUEST - ClassId:', classId);
    console.log('🚀 CANCEL REQUEST - Data:', JSON.stringify(data, null, 2));
    console.log(this.eventService);
    try {
      const result = await this.eventService.deleteEventsByClassId(
        classId,
        data
      );

      // Return appropriate response based on cancel type
      let message;
      if (data.cancelType === "refund") {
        message = "Slot cancelled and students will be refunded";
      } else if (data.cancelType === "rearrange") {
        message = "Slot marked for rearrangement";
      } else {
        message = "Slot cancelled successfully";
      }

      console.log('✅ CANCEL SUCCESS - Message:', message);
      res.status(200).json({
        success: true,
        message: message,
        result: result,
      });
    } catch (error) {
      console.log('❌ CANCEL ERROR:', error.message);
      res.status(500).json({
        success: false,
        message: error.message,
      });
    }
  }

  async deleteEventsByCenterId(req, res) {
    const { centerId } = req.params;
    try {
      await this.eventService.deleteEventsByCenterId(centerId); // Updated reference
      res.status(204).send(); // No content
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  }

  async getEventsByParentId(req, res) {
    try {
      const { parentId } = req.params;
      const events = await this.eventService.getEventsByParentId(parentId);
      res.status(200).json({ success: true, events });
    } catch (error) {
      res.status(500).json({ success: false, message: error.message });
    }
  }

  // New method to get event dates for a month (for calendar dots)
  async getEventDatesForMonth(req, res) {
    try {
      const { year, month, coachId, centerId } = req.query;

      if (!year || !month) {
        return res.status(400).json({
          success: false,
          message: 'year and month are required'
        });
      }

      if (!coachId && !centerId) {
        return res.status(400).json({
          success: false,
          message: 'Either coachId or centerId is required'
        });
      }

      console.log(`Getting event dates for ${coachId ? 'coach ' + coachId : 'center ' + centerId} for ${month}/${year}`);

      const dates = await this.eventService.getEventDatesForMonth(
        parseInt(year),
        parseInt(month),
        coachId,
        centerId
      );

      console.log(`Found event dates: ${dates}`);

      return res.status(200).json({
        success: true,
        data: dates
      });
    } catch (error) {
      console.error('Error getting event dates:', error);
      return res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Other methods can be added for fetching or updating events
}

module.exports = EventController;
