const TransactionRepo = require("../repo/transactionRepo");

const transactionRepo = new TransactionRepo();

class TransactionController {
  constructor() {
    this.getMonthlyTotal = this.getMonthlyTotal.bind(this);
    this.getDailyTransactionsForCenter = this.getDailyTransactionsForCenter.bind(this);
    this.getTotalEarnings = this.getTotalEarnings.bind(this);
  }

  async getMonthlyTotal(req, res) {
    try {
      const { centerId } = req.params;
      const { year, month } = req.query;

      if (!centerId || !year || !month) {
        return res.status(400).json({ 
          success: false, 
          message: 'centerId, year, and month are required' 
        });
      }

      console.log(`Getting monthly total for center ${centerId} for ${month}/${year}`);

      const total = await transactionRepo.getMonthlyTotal(
        centerId,
        parseInt(year),
        parseInt(month)
      );

      console.log(`Monthly total result for center ${centerId}: ${total}`);

      return res.status(200).json({
        success: true,
        data: {
          total,
          currency: 'HKD',
          year: parseInt(year),
          month: parseInt(month)
        }
      });
    } catch (error) {
      console.error('Error in getMonthlyTotal:', error);
      return res.status(500).json({ 
        success: false, 
        message: 'Failed to get monthly total',
        error: error.message 
      });
    }
  }

  async getTotalEarnings(req, res) {
    try {
      const { centerId } = req.params;

      if (!centerId) {
        return res.status(400).json({ 
          success: false, 
          message: 'centerId is required' 
        });
      }

      console.log(`Getting total earnings for center ${centerId}`);

      const total = await transactionRepo.getTotalEarnings(centerId);

      console.log(`Total earnings result for center ${centerId}: ${total}`);

      return res.status(200).json({
        success: true,
        data: {
          total,
          currency: 'HKD'
        }
      });
    } catch (error) {
      console.error('Error in getTotalEarnings:', error);
      return res.status(500).json({ 
        success: false, 
        message: 'Failed to get total earnings',
        error: error.message 
      });
    }
  }

  // New method to get daily transactions for a center
  async getDailyTransactionsForCenter(req, res) {
    try {
      const { centerId } = req.params;
      const { date } = req.query; // Expected format: YYYY-MM-DD

      if (!centerId || !date) {
        return res.status(400).json({ 
          success: false, 
          message: 'centerId and date (YYYY-MM-DD) are required' 
        });
      }

      // Validate date format if necessary (e.g., using a library like moment or regex)
      // For simplicity, we assume date is in correct format for now.

      const transactions = await transactionRepo.getDailyTransactionsForCenter(centerId, date);

      return res.status(200).json({
        success: true,
        data: transactions
      });

    } catch (error) {
      console.error('Error in getDailyTransactionsForCenter:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to get daily transactions for center',
        error: error.message
      });
    }
  }

  // New method to get transaction dates for a month (for calendar dots)
  async getTransactionDatesForMonth(req, res) {
    try {
      const { centerId } = req.params;
      const { year, month } = req.query;

      if (!centerId || !year || !month) {
        return res.status(400).json({
          success: false,
          message: 'centerId, year, and month are required'
        });
      }

      console.log(`Getting transaction dates for center ${centerId} for ${month}/${year}`);

      const dates = await transactionRepo.getTransactionDatesForMonth(
        centerId,
        parseInt(year),
        parseInt(month)
      );

      console.log(`Found transaction dates for center ${centerId}: ${dates}`);

      return res.status(200).json({
        success: true,
        data: dates
      });
    } catch (error) {
      console.error('Error getting transaction dates:', error);
      return res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
}

// Export a new instance of the controller
module.exports = new TransactionController();
